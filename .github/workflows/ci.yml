name: CI

on:
  push:
    branches:
      - master

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        dotnet-version: [ '6.0.x' ]

    steps:
      - uses: actions/checkout@v3
      - name: Setup .NET Core SDK ${{ matrix.dotnet-version }}
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: ${{ matrix.dotnet-version }}
      - name: Install Dependencies
        run: dotnet restore
      - name: Build
        run: dotnet build --configuration Release --no-restore
