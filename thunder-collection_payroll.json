{"client": "Thunder Client", "collectionName": "payroll", "dateExported": "2023-08-07T18:04:11.452Z", "version": "1.1", "folders": [{"_id": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "department", "containerId": "", "created": "2023-07-11T18:55:08.333Z", "sortNum": 10000}, {"_id": "82962c16-f041-4e28-aeeb-17e4cb1fa37f", "name": "position", "containerId": "", "created": "2023-07-11T18:55:08.334Z", "sortNum": 20000}, {"_id": "76340acc-7205-4f1c-940b-bc83fff49e6e", "name": "employee", "containerId": "", "created": "2023-07-11T18:55:08.335Z", "sortNum": 30000}, {"_id": "383ea3f5-70a3-45f6-8b0c-095624bafd36", "name": "setting", "containerId": "", "created": "2023-07-16T16:19:58.352Z", "sortNum": 40000}, {"_id": "2659826f-4616-447b-a1b2-bdf14e15d312", "name": "authen", "containerId": "", "created": "2023-07-28T05:21:36.365Z", "sortNum": 50000}, {"_id": "40eec5ba-5cdb-42bd-81f8-888e5cbcd7b5", "name": "Income", "containerId": "", "created": "2023-07-29T07:40:41.233Z", "sortNum": 60000}, {"_id": "6ac86629-2996-4735-afc4-d9473bcce122", "name": "Outcome", "containerId": "", "created": "2023-07-29T08:08:09.127Z", "sortNum": 70000}], "requests": [{"_id": "86333c14-c480-4106-b9d5-066eff6ba73d", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "get_departments", "url": "https://localhost:{{PORT}}/api/Departments", "method": "GET", "sortNum": 10000, "created": "2023-07-11T18:55:08.333Z", "modified": "2023-08-07T13:01:17.928Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "tests": []}, {"_id": "1776a7d9-124a-46c8-a284-2c68a5cf0e0d", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "82962c16-f041-4e28-aeeb-17e4cb1fa37f", "name": "create_position", "url": "https://localhost:{{PORT}}/api/Positions", "method": "POST", "sortNum": 10000, "created": "2023-07-11T18:55:08.337Z", "modified": "2023-07-28T06:55:17.515Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "json", "raw": "{\n  \"active\": true,\n  \"name\": \"หัวหน้า\",\n  \"departmentId\": 4\n}", "form": []}, "tests": []}, {"_id": "68cf3370-f806-47ad-8777-a11378bd0cef", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "383ea3f5-70a3-45f6-8b0c-095624bafd36", "name": "get_settings", "url": "https://localhost:{{PORT}}/api/Settings", "method": "GET", "sortNum": 10000, "created": "2023-07-16T16:20:09.512Z", "modified": "2023-08-06T13:24:03.686Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}", "isDisabled": true}], "params": [], "tests": []}, {"_id": "e400df5b-f9be-47b1-ac86-44f402530705", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "40eec5ba-5cdb-42bd-81f8-888e5cbcd7b5", "name": "get_incomes", "url": "https://localhost:{{PORT}}/api/Incomes", "method": "GET", "sortNum": 10000, "created": "2023-07-29T07:40:46.792Z", "modified": "2023-07-29T07:41:03.924Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "tests": []}, {"_id": "eecd4c0d-fa28-4888-9fa5-10f6dacdec72", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "6ac86629-2996-4735-afc4-d9473bcce122", "name": "get_outcomes", "url": "https://localhost:{{PORT}}/api/Outcomes", "method": "GET", "sortNum": 10000, "created": "2023-07-29T08:05:41.058Z", "modified": "2023-07-29T08:06:01.983Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "tests": []}, {"_id": "1a183e9d-1206-4332-a987-19faf5e9f55c", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "get_department_detail", "url": "https://localhost:{{PORT}}/api/Departments/3", "method": "GET", "sortNum": 15000, "created": "2023-07-11T18:55:08.336Z", "modified": "2023-07-28T06:55:39.522Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "tests": []}, {"_id": "ad361488-6ea2-436c-b056-a60b7ce6c113", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "383ea3f5-70a3-45f6-8b0c-095624bafd36", "name": "get_readonly_settings", "url": "https://localhost:{{PORT}}/api/Settings/Readonly", "method": "GET", "sortNum": 15000, "created": "2023-07-17T18:43:05.023Z", "modified": "2023-07-28T06:54:23.259Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "tests": []}, {"_id": "915a3b3b-30e8-46d1-b0d7-145857d8ae1e", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "40eec5ba-5cdb-42bd-81f8-888e5cbcd7b5", "name": "get_income_page", "url": "https://localhost:{{PORT}}/api/Incomes/page?pageNumber=1&pageSize=10", "method": "GET", "sortNum": 15000, "created": "2023-07-30T08:37:45.013Z", "modified": "2023-07-30T08:48:34.048Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [{"name": "pageNumber", "value": "1", "isPath": false}, {"name": "pageSize", "value": "10", "isPath": false}], "tests": []}, {"_id": "18da412f-71e8-40a8-b44c-2850fe514ede", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "create_department", "url": "https://localhost:{{PORT}}/api/Departments", "method": "POST", "sortNum": 20000, "created": "2023-07-11T18:55:08.334Z", "modified": "2023-07-28T06:55:48.909Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "json", "raw": "{\n  \"code\": \"CODE005\",\n  \"name\": \"บุคคล\"\n}", "form": []}, "tests": []}, {"_id": "ff62a9fd-f285-4233-b8bc-5729cf279c03", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "383ea3f5-70a3-45f6-8b0c-095624bafd36", "name": "save_settings", "url": "https://localhost:{{PORT}}/api/Settings", "method": "POST", "sortNum": 20000, "created": "2023-07-17T16:17:21.131Z", "modified": "2023-07-28T06:54:38.810Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "json", "raw": "{\n  \"CompanyName\": {\n    \"id\": 1,\n    \"name\": \"CompanyName\",\n    \"value\": \"1988 Cafe\",\n    \"type\": \"General\",\n    \"userID\": 1\n  },\n  \"Email\": {\n    \"id\": 3,\n    \"name\": \"Email\",\n    \"value\": \"<EMAIL>\",\n    \"type\": \"Contact\",\n    \"userID\": 1\n  },\n  \"Website\": {\n    \"id\": 6,\n    \"name\": \"Website\",\n    \"value\": \"www.1988Cafe.com\",\n    \"type\": \"Contact\",\n    \"userID\": 1\n  }\n}", "form": []}, "tests": []}, {"_id": "dd68391d-65e0-4611-8c71-d36c5dac683d", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "40eec5ba-5cdb-42bd-81f8-888e5cbcd7b5", "name": "create_income", "url": "https://localhost:{{PORT}}/api/Incomes/15", "method": "PUT", "sortNum": 20000, "created": "2023-07-30T06:16:01.529Z", "modified": "2023-07-30T06:17:55.288Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "json", "raw": "{\n   \"id\": 15,\n    \"name\": \"ทดสอบ2\",\n    \"engName\": \"TEST2\",\n    \"active\": false\n}", "form": []}, "tests": []}, {"_id": "a8c1c5c1-f71e-4edd-b671-0237d51e250f", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "delete_department", "url": "https://localhost:{{PORT}}/api/Departments/2", "method": "DELETE", "sortNum": 30000, "created": "2023-07-11T18:55:08.335Z", "modified": "2023-07-28T06:55:56.905Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "json", "raw": "{\n  \"code\": \"CODE001\",\n  \"name\": \"TEST\"\n}", "form": []}, "tests": []}, {"_id": "1f68cc7d-9fbb-459e-b8d8-64c3b698d319", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "40eec5ba-5cdb-42bd-81f8-888e5cbcd7b5", "name": "update_income", "url": "https://localhost:{{PORT}}/api/Incomes", "method": "POST", "sortNum": 30000, "created": "2023-07-30T06:17:12.630Z", "modified": "2023-07-30T06:17:12.630Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "json", "raw": "{\n  \"name\": \"ทดสอบ2\",\n  \"engName\": \"TEST2\",\n  \"active\": true\n}", "form": []}, "tests": []}, {"_id": "df6b9d3f-18b8-41bc-abad-d084e8429b39", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "76340acc-7205-4f1c-940b-bc83fff49e6e", "name": "download_excel", "url": "https://localhost:{{PORT}}/api/Employee/DownloadCreateEmployeeExcel?response_type=blob", "method": "GET", "sortNum": 40000, "created": "2023-07-11T18:55:08.338Z", "modified": "2023-07-28T06:54:57.508Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [{"name": "response_type", "value": "blob", "isPath": false}], "tests": []}, {"_id": "3c6dd9c5-1a09-4f5c-9fff-7f7e6539d905", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "76340acc-7205-4f1c-940b-bc83fff49e6e", "name": "download_report", "url": "https://localhost:{{PORT}}/api/Employee/Report", "method": "GET", "sortNum": 45000, "created": "2023-08-03T08:44:21.940Z", "modified": "2023-08-03T08:45:27.022Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "tests": []}, {"_id": "4dd2f929-8cd6-456f-8bb4-d7217deff045", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "76340acc-7205-4f1c-940b-bc83fff49e6e", "name": "create_employee", "url": "https://localhost:{{PORT}}/api/Employee", "method": "POST", "sortNum": 50000, "created": "2023-07-11T18:55:18.178Z", "modified": "2023-07-28T06:55:07.568Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "json", "raw": "{\n  \"id\": 0,\n  \"firstName\": \"สมชาย\",\n  \"lastName\": \"สายลม\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"123456789\",\n  \"active\": true,\n  \"departmentID\": 1\n}", "form": []}, "tests": []}, {"_id": "7ce20f1e-2867-4ecf-910f-197dd6199246", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "2659826f-4616-447b-a1b2-bdf14e15d312", "name": "authen", "url": "https://localhost:{{PORT}}/api/Users/<USER>", "method": "POST", "sortNum": 60000, "created": "2023-07-28T05:32:02.632Z", "modified": "2023-07-28T06:50:52.225Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"username\": \"<EMAIL>\",\n  \"password\": \"test\"\n}", "form": []}, "tests": [{"type": "set-env-var", "custom": "json.jwtToken", "action": "setto", "value": "{{TOKEN}}"}]}, {"_id": "917c38f1-cb4f-4c5c-b900-53eb766e7227", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "76340acc-7205-4f1c-940b-bc83fff49e6e", "name": "upload_employee_excel", "url": "https://localhost:{{PORT}}/api/Employee/UploadEmployeeExcel", "method": "POST", "sortNum": 60000, "created": "2023-08-07T14:53:11.352Z", "modified": "2023-08-07T17:36:40.576Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "formdata", "raw": "", "form": [], "files": [{"name": "formFile", "value": "c:\\Users\\<USER>\\Downloads\\thunder-file_52020f79.xlsx"}]}, "tests": []}, {"_id": "3d658022-f2cd-49b2-a27a-a951a8caa4c2", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "2659826f-4616-447b-a1b2-bdf14e15d312", "name": "refresh_token", "url": "https://localhost:{{PORT}}/api/Users/<USER>", "method": "POST", "sortNum": 70000, "created": "2023-07-28T05:58:52.232Z", "modified": "2023-08-07T13:02:06.490Z", "headers": [], "params": [], "tests": [{"type": "set-env-var", "custom": "json.jwtToken", "action": "setto", "value": "{{TOKEN}}"}]}, {"_id": "48959cce-1309-4adf-81e8-bcb4efa053ca", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "2659826f-4616-447b-a1b2-bdf14e15d312", "name": "revoke_token", "url": "https://localhost:{{PORT}}/api/Users/<USER>", "method": "POST", "sortNum": 80000, "created": "2023-07-28T06:05:24.776Z", "modified": "2023-08-05T15:12:21.609Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "body": {"type": "json", "raw": "{\n  \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjIiLCJuYmYiOjE2OTEwODAyMTQsImV4cCI6MTY5MTA4MDMzNCwiaWF0IjoxNjkxMDgwMjE0fQ.tyOLDRCUhLoNcFfTEsnmcOBmzTboLaFdiguDjhC6_8c\"\n}", "form": []}, "tests": []}, {"_id": "4e766323-d44c-4ae5-967b-0eb666365256", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "2659826f-4616-447b-a1b2-bdf14e15d312", "name": "get_user", "url": "https://localhost:{{PORT}}/api/Users/<USER>", "method": "GET", "sortNum": 90000, "created": "2023-07-28T14:05:38.164Z", "modified": "2023-07-28T14:06:07.730Z", "headers": [{"name": "Authorization", "value": "{{TOKEN}}"}], "params": [], "tests": []}]}