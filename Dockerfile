#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.
ARG VERSION=8.0
# Use the latest .NET 8.0 runtime image which includes newer glibc
FROM mcr.microsoft.com/dotnet/aspnet:$VERSION-bookworm-slim AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:$VERSION-bookworm-slim AS build
WORKDIR /src
COPY . .
RUN dotnet restore "payroll-api/payroll-api.csproj" -nowarn:msb3202,nu1503
WORKDIR "/src/payroll-api"
RUN dotnet build "payroll-api.csproj" --no-restore -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "payroll-api.csproj" --no-restore -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create the uploads directory within the container
RUN mkdir /app/uploads

ENTRYPOINT ["dotnet", "payroll-api.dll"]