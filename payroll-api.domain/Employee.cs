﻿using payroll_api.domain.Payrolls;

namespace payroll_api.domain
{
    public class Employee : EntityBase
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public bool Active { get; set; }
        public decimal Salary { get; set; }
        public DateTime StartDate { get; set; }
        public int EmployeeType { get; set; }
        public bool IsDelete { get; set; }
        public string Code { get; set; }
        public bool IsSocialSecurity { get; set; }
        public string Bank { get; set; }
        public string BankAccount { get; set; }

        public int DepartmentId { get; set; }
        public Department Department { get; set; }
        public EmployeeAddress Address { get; set; }
        public int? PositionId { get; set; }
        public Position? Position { get; set; }
        public ICollection<PayrollEmployee> PayrollEmployees { get; set; }
        public int? UserId { get; set; }
        public User? User { get; set; }
        public int? BranchId { get; set; }
        public Branch Branch { get; set; }
    }
}
