﻿using payroll_api.domain.Payrolls;
using payroll_api.domain.Settings;
using System.Text.Json.Serialization;

namespace payroll_api.domain
{
    public class User : EntityBase
    {
        public User()
        {
            EmailVerificationTokens = new List<EmailVerificationToken>();
        }

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime ExpireDate { get; set; }
        [JsonIgnore]
        public string PasswordHash { get; set; }
        public int UserType { get; set; }
        public int PackageType { get; set; }
        public bool IsEmailVerified { get; set; }


        public ICollection<Setting> Settings { get; set; }
        public ICollection<SettingImage> SettingImages { get; set; }
        public ICollection<Payroll> Payrolls { get; set; }
        public ICollection<PayrollHistory> PayrollHistories { get; set; }
        public ICollection<Income> Incomes { get; set; }
        public ICollection<Outcome> Outcomes { get; set; }
        public ICollection<Employee> Employees { get; set; }
        public ICollection<Department> Departments { get; set; }

        [JsonIgnore]
        public List<RefreshToken> RefreshTokens { get; set; } = new List<RefreshToken>();
        public List<EmailVerificationToken> EmailVerificationTokens { get; set; }
    }
}
