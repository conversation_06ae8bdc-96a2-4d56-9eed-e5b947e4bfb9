﻿using payroll_api.utility.Enum;

namespace payroll_api.domain.Payrolls
{
    public class Payroll : EntityBase
    {
        public string Code { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? PayDate { get; set; }
        public DateTime PayrollDate { get; set; }
        public decimal TotalSalary { get; set; }
        public decimal TotalIncome { get; set; }
        public decimal TotalOutcome { get; set; }
        public decimal TotalSocialSecurityFund { get; set; }
        public decimal TotalTax { get; set; }
        public decimal Total { get; set; }
        public PayrollStatus Status { get; set; }
        public string PayMethod { get; set; }
        public DateTime DueDate { get; set; }
        public decimal SocialSecurityFundEmployer { get; set; }

        public IList<PayrollEmployee> PayrollEmployees { get; set; } = new List<PayrollEmployee>();
        public IList<PayrollHistory> PayrollHistories { get; set; } = new List<PayrollHistory>();
        public int UserId { get; set; }
        public User User { get; set; }
    }
}
