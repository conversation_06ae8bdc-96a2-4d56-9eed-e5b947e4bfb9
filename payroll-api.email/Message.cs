﻿using MimeKit;

namespace payroll_api.email
{
    public class Message
    {
        public Message(IEnumerable<string> to, string subject, string content)
        {
            To = new List<MailboxAddress>();
            To.AddRange(to.Select(x => new MailboxAddress(x, x)));
            Subject = subject;
            Content = content;
        }

        public string Subject { get; set; }
        public string Content { get; set; }
        public List<MailboxAddress> To { get; set; }
    }
}
