﻿using Dapper;
using payroll_api.data;
using payroll_api.Infrastructure.Service;
using payroll_api.utility.Enum;
using payroll_api.utility.ViewModel.Dashboards;

namespace payroll_api.service;

public class DashboardService : IDashboardService
{
    private readonly DapperContext _dapperContext;
    private readonly IIdentityService _identityService;

    public DashboardService(DapperContext dapperContext, IIdentityService identityService)
    {
        _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
        _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
    }

    public async Task<AllSummaryViewModel> GetAllSummary()
    {
        var salaryByBranch = await GetSalaryByBranch();
        var salaryByPosition = await GetSalaryByPosition();
        var salaryByDepartment = await GetSalaryByDepartment();
        var salaryByYear = await GetSalaryByYear();
        var salarySummary = await GetSalarySummary();
        var top10EmployeeSalary = await GetTop10EmployeeSalary();
        return new AllSummaryViewModel
        {
            SalaryByBranch = salaryByBranch,
            SalaryByPosition = salaryByPosition,
            SalaryByDepartment = salaryByDepartment,
            SalaryByYear = salaryByYear,
            SalarySummary = salarySummary,
            Top10EmployeeSalary = top10EmployeeSalary
        };
    }

    public async Task<IEnumerable<SalaryByPositionViewModel>> GetSalaryByPosition(int departmentId = 0)
    {
        using var connection = _dapperContext.CreateConnection();
        var sql = @"
                SELECT
                    p.Id AS PositionId, 
                    p.Name, 
                    SUM(ISNULL(e.Salary, 0)) AS TotalSalary
                FROM Positions p
                    LEFT JOIN Employees e ON e.PositionId = p.Id
                WHERE e.UserId = @UserId AND PositionId > 0 AND (e.DepartmentId = @DepartmentId OR @DepartmentId = 0)
                GROUP BY p.Id, p.Name

                UNION ALL

                SELECT 
                    0 AS PositionId, 
                    N'ไม่ระบุ' AS [Name], 
                    sum(salary) 
                FROM Employees 
                WHERE (PositionId IS NULL OR PositionId = 0) AND UserId = @UserId AND @DepartmentId = 0
                HAVING SUM(Salary) IS NOT NULL

                ORDER BY TotalSalary DESC;
            ";
        var userId = _identityService.GetUserId();
        var result = await connection.QueryAsync<SalaryByPositionViewModel>(sql, new { UserId = userId, DepartmentId = departmentId });
        return result;
    }

    public async Task<IEnumerable<SalaryByBranchViewModel>> GetSalaryByBranch()
    {
        using var connection = _dapperContext.CreateConnection();
        var sql = @"
                SELECT
                    b.Id AS BranchId, 
                    b.Name, 
                    SUM(ISNULL(e.Salary, 0)) AS TotalSalary
                FROM Branchs b
                    LEFT JOIN Employees e ON e.BranchId = b.Id
                WHERE b.UserId = @UserId AND BranchId > 0
                GROUP BY b.Id, b.Name

                UNION ALL

                SELECT 
                    0 AS BranchId, 
                    N'ไม่ระบุ' AS [Name], 
                    sum(salary) 
                FROM Employees 
                WHERE (BranchId IS NULL OR BranchId = 0) AND UserId = @UserId

                ORDER BY TotalSalary DESC;
            ";
        var userId = _identityService.GetUserId();
        var result = await connection.QueryAsync<SalaryByBranchViewModel>(sql, new { UserId = userId });
        return result;
    }

    public async Task<IEnumerable<SalaryByDepartmentViewModel>> GetSalaryByDepartment()
    {
        using var connection = _dapperContext.CreateConnection();
        var sql = @"
               SELECT
                    d.Id AS DepartmentId, d.Name, SUM(ISNULL(e.Salary, 0)) AS TotalSalary
                FROM Departments d
                    LEFT JOIN Employees e ON e.DepartmentId = d.Id
                WHERE d.UserId = @UserId
                GROUP BY d.Id, d.Name
                ORDER BY TotalSalary DESC
            ";
        var userId = _identityService.GetUserId();
        var result = await connection.QueryAsync<SalaryByDepartmentViewModel>(sql, new { UserId = userId });
        return result;
    }

    public async Task<IEnumerable<SalaryByYearViewModel>> GetSalaryByYear(int? year = null)
    {
        year ??= DateTime.Now.Year;
        
        using var conn = _dapperContext.CreateConnection();
        var sql = $@"
                SELECT MONTH(PayrollDate) AS Month, 
	                SUM(Total) AS Total, 
	                SUM(TotalTax) As TotalTax, 
	                SUM(TotalSocialSecurityFund) As TotalSocialSecurityFund
                FROM Payrolls WITH (NOLOCK)
                WHERE 
                    UserId = @UserId AND 
                    Status = @Status AND 
                    (YEAR(PayrollDate) = @Year OR @Year IS NULL)
                GROUP BY MONTH(PayrollDate)
            ";
        var userId = _identityService.GetUserId();
        var result = (await conn.QueryAsync<SalaryByYearViewModel>(sql, new { UserId = userId, Year = year, Status = PayrollStatus.Pay })).ToList();

        var months = new List<string>() {
                "มกราคม",
                "กุมภาพันธ์",
                "มีนาคม",
                "เมษายน",
                "พฤษภาคม",
                "มิถุนายน",
                "กรกฎาคม",
                "สิงหาคม",
                "กันยายน",
                "ตุลาคม",
                "พฤศจิกายน",
                "ธันวาคม",
            };
        for (var i = 1; i <= months.Count; i++)
        {
            var current = result.Where(x => x.Month == i).FirstOrDefault();
            if (current is null)
                result.Add(new SalaryByYearViewModel { Month = i, Total = 0, TotalSocialSecurityFund = 0, TotalTax = 0, MonthName = months[i - 1] });
            else
                current.MonthName = months[i - 1];
        }

        result = result.OrderBy(x => x.Month).ToList();
        return result;
    }

    public async Task<SalarySummaryViewModel> GetSalarySummary()
    {
        using var conn = _dapperContext.CreateConnection();
        var sql = @"
                SELECT count(*) FROM Employees WHERE Active = 1 AND UserId = @UserId;
                SELECT 
	                SUM(TotalSalary + TotalIncome - TotalOutcome) AS TotalSalary,
	                SUM(TotalTax) AS TotalTax, 
	                SUM(TotalSocialSecurityFund + SocialSecurityFundEmployer) AS TotalSocialSecurityFund
                FROM Payrolls WITH(NOLOCK)
                WHERE UserId = @UserId 
                    AND YEAR(PayrollDate) = @Year
                    AND Status = @Status
            ";
        var userId = _identityService.GetUserId();
        var year = DateTime.Now.Year;
        using var multi = await conn.QueryMultipleAsync(sql, new { UserId = userId, Year = year, Status = PayrollStatus.Pay });
        var result = new SalarySummaryViewModel();
        var totalEmployee = (await multi.ReadAsync<int>()).FirstOrDefault();
        result = (await multi.ReadAsync<SalarySummaryViewModel>()).FirstOrDefault();
        result.TotalEmployee = totalEmployee;
        return result;
    }

    public async Task<IEnumerable<Top10EmployeeSalaryViewModel>> GetTop10EmployeeSalary(int departmentId = 0)
    {
        using var connection = _dapperContext.CreateConnection();
        var sql = @"
            SELECT TOP 10 
                e.FirstName + ' ' + e.LastName AS Name,
                e.Salary,
                ISNULL(p.Name, '') AS Position
            FROM Employees e
            LEFT JOIN Positions p ON p.id = e.PositionId
            WHERE 
                e.UserId = @UserId AND
                (e.DepartmentId = @DepartmentId OR @DepartmentId = 0)
            ORDER BY e.Salary DESC
        ";
        var userId = _identityService.GetUserId();
        var result = await connection.QueryAsync<Top10EmployeeSalaryViewModel>(sql, new { UserId = userId, DepartmentId = departmentId });
        return result;
    }
}
