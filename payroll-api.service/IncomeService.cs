﻿using AutoMapper;
using Dapper;
using Microsoft.EntityFrameworkCore;
using payroll_api.data;
using payroll_api.data.Infrastructure;
using payroll_api.data.Repository;
using payroll_api.domain;
using payroll_api.Infrastructure.Service;
using payroll_api.service.Infrastructure.Helper;
using payroll_api.utility.Constant;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.service
{

    public class IncomeService : IIncomeService
    {
        private readonly IIncomeRepository _incomeRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IIdentityService _identityService;
        private readonly DapperContext _dapperContext;

        public IncomeService(
            IIncomeRepository incomeRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IIdentityService identityService,
            DapperContext dapperContext
        )
        {
            _incomeRepository = incomeRepository ?? throw new ArgumentNullException(nameof(incomeRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
        }

        public async Task CreateIncome(IncomeViewModel incomeViewModel)
        {
            var userId = _identityService.GetUserId();

            // Validate
            var isExistCode = await _incomeRepository.All.AnyAsync(i => i.EngName == incomeViewModel.EngName && i.UserId == userId);
            if (isExistCode) throw new InvalidOperationException("รหัสนี้ถูกใช้งานเเล้ว");

            var income = _mapper.Map<Income>(incomeViewModel);

            income.UserId = userId;

            _incomeRepository.Add(income);
            await _unitOfWork.CommitAsync();
        }

        public async Task GenerateSystemIncomes(int userId)
        {
            var incomes = new List<Income>() {
                new() { SystemGenerate = true, Name = IncomeKeys.SAL, EngName = nameof(IncomeKeys.SAL), Active = true, UserId = userId },
                new() { SystemGenerate = true, Name = IncomeKeys.COM, EngName = nameof(IncomeKeys.COM), Active = false, UserId = userId },
                new() { SystemGenerate = true, Name = IncomeKeys.OT, EngName = nameof(IncomeKeys.OT), Active = false, UserId = userId },
                new() { SystemGenerate = true, Name = IncomeKeys.ALW, EngName = nameof(IncomeKeys.ALW), Active = false, UserId = userId },
                new() { SystemGenerate = true, Name = IncomeKeys.DALW, EngName = nameof(IncomeKeys.DALW), Active = false, UserId = userId },
                new() { SystemGenerate = true, Name = IncomeKeys.BFD, EngName = nameof(IncomeKeys.BFD), Active = false, UserId = userId },
                new() { SystemGenerate = true, Name = IncomeKeys.BHR, EngName = nameof(IncomeKeys.BHR), Active = false, UserId = userId },
                new() { SystemGenerate = true, Name = IncomeKeys.BTS, EngName = nameof(IncomeKeys.BTS), Active = false, UserId = userId },
                new() { SystemGenerate = true, Name = IncomeKeys.BMP, EngName = nameof(IncomeKeys.BMP), Active = false, UserId = userId },
                new() {  SystemGenerate = true, Name = IncomeKeys.BCL, EngName = nameof(IncomeKeys.BCL), Active = false, UserId = userId },
                new() {  SystemGenerate = true, Name = IncomeKeys.POS, EngName = nameof(IncomeKeys.POS), Active = false, UserId = userId },
                new() {  SystemGenerate = true, Name = IncomeKeys.BNS, EngName = nameof(IncomeKeys.BNS), Active = false, UserId = userId }
            };

            _incomeRepository.AddRange(incomes);
            await _unitOfWork.CommitAsync();
        }

        public async Task<PageResponseViewModel<List<IncomeViewModel>>> GetIncomePage(PageFilterViewModel pageFilter)
        {
            var userId = _identityService.GetUserId();
            using var conn = _dapperContext.CreateConnection();

            var baseSql = @"
                SELECT
                    Id,
                    Name,
                    EngName,
                    Active,
                    SystemGenerate,
                    ISNULL(TutorialStepId, 0) AS TutorialStepId
                FROM Incomes
            ";
            var sqlFilter = "WHERE UserId = @UserId";

            if (!string.IsNullOrWhiteSpace(pageFilter.SearchTerm))
            {
                pageFilter.SearchTerm += "%";
                sqlFilter = " WHERE Email LIKE @SearchTerm OR FirstName + ' ' + LastName LIKE @SearchTerm";
            }

            sqlFilter += pageFilter.GetSortQuery("ORDER BY Id");

            var sqlCount = "SELECT COUNT(1) FROM Incomes WHERE UserId = @UserId";
            var total = await conn.ExecuteScalarAsync<int>(sqlCount, new { UserId = userId });

            var sql = baseSql + sqlFilter + " OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY";
            var pageData = (await conn.QueryAsync<IncomeViewModel>(
                sql,
                new
                {
                    UserId = userId,
                    pageFilter.SearchTerm,
                    Offset = pageFilter.PageIndex * pageFilter.PageSize,
                    Limit = pageFilter.PageSize
                })
            )
            .ToList();

            var result = PageHelper.CreatePagedReponse(
                pageData,
                pageFilter,
                total
            );

            return result;
        }

        public async Task<IEnumerable<IncomeViewModel>> GetIncomes(bool? active = null)
        {
            var userId = _identityService.GetUserId();
            using var conn = _dapperContext.CreateConnection();
            var sql = @"
                SELECT 
                    Id,
                    Name,
                    EngName,
                    Active,
                    SystemGenerate
                FROM Incomes
                WHERE UserId = @UserId AND (Active = @Active OR @Active IS NULL)
            ";

            var result = await conn.QueryAsync<IncomeViewModel>(sql, new { UserId = userId, Active = active });
            return result.ToList();
        }

        public async Task UpdateActiveIncome(int id, UpdateIncomeActiveViewModel updateIncomeActiveViewModel)
        {
            var income = await _incomeRepository.GetAll().FirstOrDefaultAsync(e => e.Id == id) ?? throw new InvalidOperationException("Income not found.");
            income.Active = updateIncomeActiveViewModel.Active;
            _incomeRepository.Update(income);
            await _unitOfWork.CommitAsync();
        }

        public async Task UpdateIncome(int ID, UpdateIncomeViewModel incomeViewModel)
        {
            await ValidateDuplicateIncome(incomeViewModel);

            var income = _incomeRepository.GetSingle(ID);
            _mapper.Map(incomeViewModel, income);
            _incomeRepository.Update(income);
            await _unitOfWork.CommitAsync();
        }

        private async Task ValidateDuplicateIncome(UpdateIncomeViewModel incomeViewModel)
        {
            var userId = _identityService.GetUserId();
            var isDuplicateIncome = await _incomeRepository.GetAll()
                .AnyAsync(x =>
                    x.UserId == userId &&
                    (x.Name == incomeViewModel.Name || x.EngName == incomeViewModel.EngName) &&
                    x.Id != incomeViewModel.ID
                );
            if (isDuplicateIncome) throw new InvalidOperationException("ชื่อหรือรหัสเงินเพิ่มนี้ถูกใช้ไปแล้ว");
        }
    }
}
