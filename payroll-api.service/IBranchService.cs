using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.service
{
    public interface IBranchService
    {
        Task<PageResponseViewModel<List<BranchViewModel>>> GetBranchPage(PageFilterViewModel pageFilter);
        Task<IEnumerable<BranchViewModel>> GetBranches();
        Task CreateBranch(BranchDetailViewModel branchViewModel);
        Task UpdateBranch(int id, UpdateBranchViewModel updateBranchViewModel);
        Task DeleteBranch(int id, bool deleteEmployee);
        Task<BranchDetailViewModel> GetBranch(int id);
        Task<Dictionary<string, int>> GetBranchesCodes(int userId);
        Task UpdateActiveBranch(int id, UpdateBranchActiveViewModel updateBranchViewModel);
    }
}
