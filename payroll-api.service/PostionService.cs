﻿using appraisal_api.utility.ViewModel.Position;
using AutoMapper;
using Dapper;
using Microsoft.EntityFrameworkCore;
using payroll_api.data;
using payroll_api.data.Infrastructure;
using payroll_api.data.Repository;
using payroll_api.domain;
using payroll_api.Infrastructure.Service;
using payroll_api.service.Infrastructure.Helper;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.service
{
    public class PostionService : IPositionService
    {
        private readonly IPositionRepository _positionRepository;
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private readonly DapperContext _dapperContext;
        private readonly IIdentityService _identityService;

        public PostionService(
            IPositionRepository positionRepository,
            IMapper mapper,
            IUnitOfWork unitOfWork,
            DapperContext dapperContext,
            IIdentityService identityService
        )
        {
            _positionRepository = positionRepository ?? throw new ArgumentNullException(nameof(positionRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
            _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
        }

        public async Task CreatePosition(PositionViewModel positionViewModel)
        {
            await ValidatePositionName(positionViewModel);

            var position = _mapper.Map<Position>(positionViewModel);
            _positionRepository.Add(position);
            await _unitOfWork.CommitAsync();
        }

        private async Task ValidatePositionName(PositionViewModel positionViewModel)
        {
            var isDuplicatePositionName = await _positionRepository
                            .GetAll()
                            .AnyAsync(x =>
                                x.Id != positionViewModel.ID &&
                                x.Name == positionViewModel.Name &&
                                x.DepartmentId == positionViewModel.DepartmentId
                            );
            if (isDuplicatePositionName) throw new InvalidOperationException("ชื่อตำแหน่งนี้ถูกใช้งานเเล้ว");
        }

        public async Task<PageResponseViewModel<List<PositionViewModel>>> GetPositionPage(PositionPageFilterViewModel pageFilterViewModel)
        {
            using var conn = _dapperContext.CreateConnection();
            var sql = @"
                with cte1 as (
	                select Id, [Name], Active
	                from Positions
	                where DepartmentId = @DepartmentId
	                order by [Name]
	                offset @PageIndex * @PageSize rows
	                fetch next @PageSize rows only
                ), cte2 as (
	                select c.Id, COUNT(e.Id) as TotalEmployee
	                from Employees e
	                inner join cte1 c on c.Id = e.PositionId
                where e.Active = 1
	                group by c.Id
                )
                select c1.*, ISNULL(c2.TotalEmployee, 0) as TotalEmployee 
                from cte1 c1
                left join cte2 c2 on c2.Id = c1.Id
            ";

            var data = await conn.QueryAsync<PositionViewModel>(sql, new
            {
                pageFilterViewModel.DepartmentId,
                pageFilterViewModel.PageSize,
                pageFilterViewModel.PageIndex
            });

            var query = _positionRepository.GetAll()
                .Where(p => p.DepartmentId == pageFilterViewModel.DepartmentId)
                .AsQueryable();

            var total = await query.CountAsync();

            var result = PageHelper.CreatePagedReponse(
                data.ToList(),
                pageFilterViewModel,
                total
            );

            return result;
        }

        public async Task<PositionDetailViewModel> GetPositions(int? departmentId)
        {
            var userId = _identityService.GetUserId();

            using var conn = _dapperContext.CreateConnection();
            var sqlPositions = @"
               with cte1 as (
	                select p.Id, p.[Name], p.Active, d.Id as DepartmentId
	                from Positions p inner join Departments d on p.DepartmentId = d.Id
	                where d.UserId = @UserId and (p.DepartmentId = @DepartmentId or @DepartmentId is null)
                ), cte2 as (
	                select c.Id, COUNT(e.Id) as TotalEmployee
	                from Employees e
	                inner join cte1 c on c.Id = e.PositionId
                where e.Active = 1
	                group by c.Id
                )
                select c1.*, ISNULL(c2.TotalEmployee, 0) as TotalEmployee 
                from cte1 c1
                left join cte2 c2 on c2.Id = c1.Id;;
            ";
            var sqlDepartmentName = @"
                select Name
                from Departments
                where Id = @DepartmentId;
            ";
            var sql = sqlDepartmentName + sqlPositions;

            using var multi = await conn.QueryMultipleAsync(sql, new { UserId = userId, DepartmentId = departmentId });
            var departmentName = await multi.ReadFirstOrDefaultAsync<string>();
            var positions = (await multi.ReadAsync<PositionViewModel>()).ToList();
            return new PositionDetailViewModel
            {
                DepartmentName = departmentName,
                Positions = positions
            };
        }

        public async Task UpdatePosition(PositionViewModel positionViewModel)
        {
            await ValidatePositionName(positionViewModel);

            var position = _positionRepository.GetSingle(positionViewModel.ID) ?? throw new InvalidOperationException("ไม่พบตำแหน่ง");
            position.Name = positionViewModel.Name;
            position.Active = positionViewModel.Active;

            await _unitOfWork.CommitAsync();
        }
    }
}
