using payroll_api.utility.ViewModel.Dashboards;

namespace payroll_api.service;

public interface IDashboardService
{
    Task<AllSummaryViewModel> GetAllSummary();
    Task<IEnumerable<SalaryByBranchViewModel>> GetSalaryByBranch();
    Task<IEnumerable<SalaryByPositionViewModel>> GetSalaryByPosition(int departmentId = 0);
    Task<IEnumerable<SalaryByDepartmentViewModel>> GetSalaryByDepartment();
    Task<IEnumerable<SalaryByYearViewModel>> GetSalaryByYear(int? year = null);
    Task<SalarySummaryViewModel> GetSalarySummary();
    Task<IEnumerable<Top10EmployeeSalaryViewModel>> GetTop10EmployeeSalary(int departmentId = 0);
}
