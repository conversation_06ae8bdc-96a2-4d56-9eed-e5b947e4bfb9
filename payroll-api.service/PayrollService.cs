﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using ClosedXML.Excel;
using Microsoft.EntityFrameworkCore;
using payroll_api.data.Infrastructure;
using payroll_api.data.Repository;
using payroll_api.domain;
using payroll_api.domain.Payrolls;
using payroll_api.Infrastructure.Service;
using payroll_api.utility.Constant;
using payroll_api.utility.Enum;
using payroll_api.utility.Helper;
using payroll_api.utility.Pattern;
using payroll_api.utility.ViewModel;
using payroll_api.utility.ViewModel.Reports;
using System.Collections.Concurrent;
using System.Data;
using System.Text;
using System.Text.RegularExpressions;

namespace payroll_api.service
{
    public class PayrollService : IPayrollService
    {
        private readonly IPayrollRepository _payrollRepository;
        private readonly IOutcomeReporitory _outcomeRepository;
        private readonly IEmployeeRepository _employeeRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IIdentityService _identityService;
        private readonly ISettingService _settingService;
        private readonly IIncomeService _incomeService;
        private readonly IOutcomeService _outcomeService;
        private readonly IIncomeRepository _incomeRepository;
        private readonly IReportService _reportService;
        private readonly IAutoEmailRepository _autoEmailRepository;

        public PayrollService(
            IPayrollRepository payrollRepository,
            IOutcomeReporitory outcomeRepository,
            IEmployeeRepository employeeRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IIdentityService identityService,
            ISettingService settingService,
            IIncomeService incomeService,
            IOutcomeService outcomeService,
            IIncomeRepository incomeRepository,
            IReportService reportService,
            IAutoEmailRepository autoEmailRepository
        )
        {
            _payrollRepository = payrollRepository ?? throw new ArgumentNullException(nameof(payrollRepository));
            _outcomeRepository = outcomeRepository ?? throw new ArgumentNullException(nameof(outcomeRepository));
            _employeeRepository = employeeRepository ?? throw new ArgumentNullException(nameof(employeeRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
            _settingService = settingService ?? throw new ArgumentNullException(nameof(settingService));
            _incomeService = incomeService ?? throw new ArgumentNullException(nameof(incomeService));
            _outcomeService = outcomeService ?? throw new ArgumentNullException(nameof(outcomeService));
            _incomeRepository = incomeRepository ?? throw new ArgumentNullException(nameof(incomeRepository));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _autoEmailRepository = autoEmailRepository ?? throw new ArgumentNullException(nameof(autoEmailRepository));
        }

        public async Task Approve(int id)
        {
            var payroll = _payrollRepository.GetSingle(id) ?? throw new InvalidOperationException("ไม่พบการจ่ายเงินเดือนนี้");
            payroll.Status = PayrollStatus.Approve;

            var payrollHistory = new PayrollHistory
            {
                UserId = _identityService.GetUserId(),
                PayrollId = 0,
                Name = PayrollHistoryKeys.Approve,
                Description = "",
                CreatedDate = DateTime.Now,
            };
            payroll.PayrollHistories.Add(payrollHistory);

            await _unitOfWork.CommitAsync();
        }

        public async Task CancelPayroll(int id)
        {
            var payroll = _payrollRepository.GetSingle(id);
            payroll.Status = PayrollStatus.Cancel;
            var payrollHistory = new PayrollHistory
            {
                UserId = _identityService.GetUserId(),
                PayrollId = 0,
                Name = PayrollHistoryKeys.Cancel,
                Description = "",
                CreatedDate = DateTime.Now,
            };
            payroll.PayrollHistories.Add(payrollHistory);

            await _unitOfWork.CommitAsync();
        }

        public async Task<int> CreatePayroll(CreatePayrollViewModel createPayrollViewModel)
        {
            var userId = _identityService.GetUserId();
            var payroll = _mapper.Map<Payroll>(createPayrollViewModel);
            payroll.PayrollDate = DateTime.Now;
            payroll.UserId = userId;

            var payrollHistory = new PayrollHistory
            {
                UserId = userId,
                PayrollId = 0,
                Description = "",
                CreatedDate = DateTime.Now,
            };
            switch (payroll.Status)
            {
                case PayrollStatus.Draft:
                    payrollHistory.Name = payroll.Id == 0 ? PayrollHistoryKeys.Create : PayrollHistoryKeys.Update;
                    break;
                case PayrollStatus.Approve:
                    payrollHistory.Name = PayrollHistoryKeys.Approve;
                    break;
                default:
                    break;
            }

            if (createPayrollViewModel.Id > 0)
            {
                payroll.Id = 0;
                var originalPayroll = await _payrollRepository
                    .AllIncluding(x => x.PayrollHistories)
                    .FirstOrDefaultAsync(x => x.Id == createPayrollViewModel.Id);
                if (originalPayroll is not null)
                {
                    foreach (var item in originalPayroll.PayrollHistories)
                    {
                        item.PayrollId = 0;
                        payroll.PayrollHistories.Add(item);
                    }
                    _payrollRepository.Delete(originalPayroll);
                }
            }

            payroll.PayrollHistories.Add(payrollHistory);

            _payrollRepository.Add(payroll);

            await _unitOfWork.CommitAsync();

            return payroll.Id;
        }

        public byte[] DownloadImportPayrollExcel()
        {
            var userId = _identityService.GetUserId();
            var incomes = _incomeRepository.GetAll().Where(x => x.UserId == userId && x.Active).AsNoTracking().ToList();
            var outcomes = _outcomeRepository.GetAll().Where(x => x.UserId == userId && x.Active).AsNoTracking().ToList();

            var baseColumns = new string[] { "รหัสพนักงาน", "ชื่อ-นามสกุล" };
            var dataTable = new DataTable();
            foreach (var item in baseColumns)
                dataTable.Columns.Add(item, typeof(string));
            foreach (var item in incomes)
                dataTable.Columns.Add($"{item.Name} ({item.EngName})", typeof(string));
            foreach (var item in outcomes)
                dataTable.Columns.Add($"{item.Name} ({item.EngName})", typeof(string));

            // Create a new workbook
            using var workbook = new XLWorkbook();
            // Add a worksheet to the workbook
            var worksheet = workbook.Worksheets.Add("Sheet1");

            // Load the data from the DataTable to the worksheet
            worksheet.Cell(1, 1).InsertTable(dataTable);
            var htFirstCell = worksheet.Cell(1, 1);
            var htLastCell = worksheet.Cell(1, dataTable.Columns.Count);
            var headersRange = worksheet.Range(htFirstCell, htLastCell);
            headersRange.Style.Font.SetFontColor(XLColor.Onyx);
            headersRange.Style.Fill.SetBackgroundColor(XLColor.FromHtml(ExcelSettingKeys.PrimaryColor));
            worksheet.Tables?.FirstOrDefault()?.SetShowAutoFilter(false);

            // Set the column width for specific columns
            var totalColumns = baseColumns.Length + incomes.Count + outcomes.Count;
            for (int i = 1; i <= totalColumns; i++)
                worksheet.Column(i).Width = 20;

            // Save the workbook to a file
            using var ms = new MemoryStream();
            workbook.SaveAs(ms);

            return ms.ToArray();
        }

        public async Task<byte[]> DownloadPayrollExcelReport(int payrollId)
        {
            var userId = _identityService.GetUserId();
            var appSetting = await _settingService.GetOptionByNameAsync(
                userId,
                SettingKey.CompanyName,
                SettingKey.CompanyPassportID
            );
            var payroll = await _payrollRepository.GetPayrollExcel(payrollId);


            var payrollEmployees = await _payrollRepository.GetPayrollEmployeeExcel(payrollId);
            var outcomeColumns = payrollEmployees?.FirstOrDefault()?.PayrollEmployeeOutcomes?.Select(x => x.Name).ToList();
            var incomeColumns = payrollEmployees?.FirstOrDefault()?.PayrollEmployeeIncomes?.Select(x => x.Name).ToList();

            DataTable dataTable = new();
            dataTable.Columns.Add("ลำดับที่", typeof(int));
            dataTable.Columns.Add("เลขที่พนักงาน", typeof(string));
            dataTable.Columns.Add("ชื่อ", typeof(string));
            dataTable.Columns.Add("นามสกุล", typeof(string));
            incomeColumns?.ForEach(name =>
            {
                dataTable.Columns.Add(name, typeof(decimal));
            });
            outcomeColumns?.ForEach(name =>
            {
                dataTable.Columns.Add(name, typeof(decimal));
            });
            dataTable.Columns.Add("รวมเงินรับ", typeof(decimal));
            dataTable.Columns.Add("รวมเงินหัก", typeof(decimal));
            dataTable.Columns.Add("เงินรับสุทธิ", typeof(decimal));

            // Create a new Excel workbook
            using var workbook = new XLWorkbook();
            // Add a worksheet
            var worksheet = workbook.Worksheets.Add("Sheet1");

            var columnsToExpand = new List<int> { 2, 3, 5, 6, 8, 9 };
            columnsToExpand.ForEach(x =>
            {
                worksheet.Column(x).Width = 18;
            });

            #region Set Title And Sub Title
            var titleRow = worksheet.Range(1, 1, 1, dataTable.Columns.Count);
            titleRow.Merge().Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            titleRow.Merge().Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
            titleRow.Value = "รายงานการจ่ายเงินเดือน";
            titleRow.Style.Font.SetFontSize(18);
            var firstRow = worksheet.Row(1);
            firstRow.Height = 36;

            var title2Row = worksheet.Row(2);
            title2Row.Cell(2).Value = "เลขที่จ่ายเงินเดือน";
            title2Row.Cell(3).Value = payroll.Code;
            title2Row.Cell(5).Value = "";
            title2Row.Cell(6).Value = "";
            title2Row.Cell(8).Value = "วันที่ออกรายงาน";
            title2Row.Cell(9).Value = DateTime.Now;
            title2Row.Cell(9).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;

            var title3Row = worksheet.Row(3);
            title3Row.Cell(2).Value = "วันที่ทำรายการ";
            title3Row.Cell(3).Value = payroll.PayrollDate.ToString("dd/MM/yyyy");
            title3Row.Cell(3).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;
            title3Row.Cell(5).Value = "กำหนดชำระวันที่";
            title3Row.Cell(6).Value = payroll.DueDate.ToString("dd/MM/yyyy");
            title3Row.Cell(6).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;
            title3Row.Cell(8).Value = "ประจำเดือน";
            title3Row.Cell(9).Value = $"{DateTimeUtils.GetThaiMonth(payroll.PayrollDate)} {DateTimeUtils.GetThaiYear(payroll.PayrollDate)}";

            var title4Row = worksheet.Row(4);
            title4Row.Cell(2).Value = "ชื่อผู้ประกอบการ";
            title4Row.Cell(3).Value = appSetting.CompanyName;
            title4Row.Cell(5).Value = "เลขประจำตัวผู้เสียภาษี";
            title4Row.Cell(6).Value = appSetting.CompanyPassportID;
            title4Row.Cell(8).Value = "ผู้ออกรายงาน";
            title4Row.Cell(9).Value = payroll.FullName;

            #endregion

            for (int i = 0; i < payrollEmployees?.Count; i++)
            {
                var payrollEmployee = payrollEmployees[i];

                var row = dataTable.NewRow();
                row["ลำดับที่"] = i + 1;
                row["เลขที่พนักงาน"] = payrollEmployee.Code;
                row["ชื่อ"] = payrollEmployee.FirstName;
                row["นามสกุล"] = payrollEmployee.LastName;
                row["รวมเงินรับ"] = payrollEmployee.TotalIncome;
                row["รวมเงินหัก"] = payrollEmployee.TotalOutcome;
                row["เงินรับสุทธิ"] = payrollEmployee.Total;

                payrollEmployee.PayrollEmployeeIncomes.ToList().ForEach(x =>
                {
                    row[x.Name] = x.Amount;
                });
                payrollEmployee.PayrollEmployeeOutcomes.ToList().ForEach(x =>
                {
                    row[x.Name] = x.Amount;
                });

                dataTable.Rows.Add(row);
            }

            var startTableRow = 6;
            worksheet.Cell(startTableRow, 1).InsertTable(dataTable);
            var rangeHeder = worksheet.Range(startTableRow, 1, startTableRow, dataTable.Columns.Count);
            rangeHeder.Style
                .Font.SetBold()
                .Border.SetOutsideBorder(XLBorderStyleValues.Thick)
                .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            for (int i = 0; i < dataTable.Columns.Count; i++)
            {
                var rangeColumn = worksheet.Range(startTableRow, i + 1, startTableRow + payrollEmployees?.Count ?? 0 + 1, i + 1);
                rangeColumn.Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                rangeColumn.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            }

            var columnsMoneyFormat = new List<string> { "E", "F", "G", "H", "I", "J", "K" };
            var startMoneyRowIndex = 7;
            columnsMoneyFormat.ForEach(x =>
            {
                var rangeMoney = worksheet.Range($"${x}${startMoneyRowIndex}:${x}{startMoneyRowIndex + payrollEmployees?.Count - 1}");
                rangeMoney.Style
                    .NumberFormat.SetFormat(CurrencyKeys.Format)
                    .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);
            });

            // Save the workbook to a file
            using var ms = new MemoryStream();
            workbook.SaveAs(ms);

            return ms.ToArray();
        }

        public async Task<string> GenerateCode(int startMonth = 0)
        {
            var runningNumber = 1;
            var userId = _identityService.GetUserId();
            var year = DateTime.Now.Year;
            var month = startMonth > 0 ? startMonth : DateTime.Now.Month;
            var latestCode = await _payrollRepository.GetLatestCode(userId, startMonth);

            if (latestCode != null)
            {
                Match match = Regex.Match(latestCode, PayrollRegex.PayrollCodeRegex);

                if (match.Success)
                {
                    string runningNumberStr = match.Groups[1].Value;
                    if (int.TryParse(runningNumberStr, out runningNumber))
                        runningNumber++;
                }
            }

            var code = $"PAY_{year}{month:D2}_{runningNumber:D2}";
            return code;
        }

        public async Task<PayrollDetailViewModel?> GetPayroll(int id, bool copy)
        {
            var userId = _identityService.GetUserId();
            var payroll = await _payrollRepository
                .AllIncluding(x => x.PayrollEmployees, x => x.PayrollHistories)
                .Where(x => x.Id == id && x.UserId == userId)
                .ProjectTo<PayrollDetailViewModel>(_mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();

            if (payroll is null) return null;

            payroll.PayrollEmployees = payroll.PayrollEmployees.OrderBy(x => x.EmployeeCode).ToList();

            if (copy)
            {
                payroll.Id = 0;
                payroll.Status = PayrollStatus.None;
                payroll.PayMethod = "";

                payroll.PayrollDate = payroll.PayrollDate.AddMonths(1);
                int startMonth = payroll.PayrollDate.Month;
                payroll.Code = await GenerateCode(startMonth);

                var nextDueDate = payroll.DueDate.AddMonths(1);
                payroll.DueDate = new DateTime(nextDueDate.Year, nextDueDate.Month, DateTime.DaysInMonth(nextDueDate.Year, nextDueDate.Month), 23, 59, 59, DateTimeKind.Unspecified);
                payroll.CreatedDate = DateTime.Now;
                payroll.PayDate = null;
                payroll.isCopy = true;

                var incomes = await _incomeService.GetIncomes(true);
                var payrollEmployeeIncomes = payroll?.PayrollEmployees?.SelectMany(x => x.PayrollEmployeeIncomes).ToList();
                var currentIncomes = payrollEmployeeIncomes?.Select(x => x.EngName).Distinct().ToList() ?? Enumerable.Empty<string>();
                var additionalIncomes = incomes.Where(x => !currentIncomes.Contains(x.EngName)).ToList();

                var outcomes = await _outcomeService.GetOutcomes(true);
                var payrollEmployeeOutcomes = payroll?.PayrollEmployees?.SelectMany(x => x.PayrollEmployeeOutcomes).ToList();
                var currentOutcomes = payrollEmployeeOutcomes?.Select(x => x.EngName).Distinct().ToList() ?? Enumerable.Empty<string>();
                var additionalOutcomes = outcomes.Where(x => !currentOutcomes.Contains(x.EngName)).ToList();

                foreach (var payrollEmployee in payroll?.PayrollEmployees ?? [])
                {
                    #region Income
                    var removeIncomes = payrollEmployee.PayrollEmployeeIncomes.Where(x => incomes.Select(o => o.EngName).Contains(x.EngName) == false).ToList();
                    var totalRemoveIncomes = removeIncomes.Sum(x => x.Amount);
                    payrollEmployee.TotalIncome -= totalRemoveIncomes;
                    payrollEmployee.Total -= totalRemoveIncomes;
                    payroll.TotalIncome -= totalRemoveIncomes;
                    payroll.Total -= totalRemoveIncomes;
                    foreach (var removeIncome in removeIncomes)
                        payrollEmployee.PayrollEmployeeIncomes.Remove(removeIncome);

                    var payrollEmployeeIncomesMapSettings = new List<PayrollEmployeeIncomeViewModel>();

                    if (additionalIncomes is not null)
                    {
                        foreach (var incomeKey in additionalIncomes)
                        {
                            payrollEmployeeIncomesMapSettings.Add(new PayrollEmployeeIncomeViewModel
                            {
                                Name = incomeKey.Name,
                                EngName = incomeKey.EngName,
                                Amount = 0
                            });
                        }
                    }

                    payrollEmployee.PayrollEmployeeIncomes.AddRange(payrollEmployeeIncomesMapSettings);
                    #endregion

                    #region Outcome
                    var removeOutcomes = payrollEmployee.PayrollEmployeeOutcomes.Where(x => !outcomes.Select(o => o.EngName).Contains(x.EngName)).ToList();
                    var totalRemoveOutcomes = removeOutcomes.Sum(x => x.Amount);
                    payrollEmployee.TotalOutcome -= totalRemoveOutcomes;
                    payrollEmployee.Total += totalRemoveOutcomes;
                    payroll.TotalOutcome -= totalRemoveOutcomes;
                    payroll.Total += totalRemoveOutcomes;
                    foreach (var removeOutcome in removeOutcomes)
                        payrollEmployee.PayrollEmployeeOutcomes.Remove(removeOutcome);

                    var payrollEmployeeOutcomeMapSettings = new List<PayrollEmployeeOutcomeViewModel>();
                    foreach (var outcomeKey in additionalOutcomes ?? [])
                    {
                        payrollEmployeeOutcomeMapSettings.Add(new PayrollEmployeeOutcomeViewModel
                        {
                            Name = outcomeKey.Name,
                            EngName = outcomeKey.EngName,
                            Amount = 0
                        });
                    }
                    payrollEmployee.PayrollEmployeeOutcomes.AddRange(payrollEmployeeOutcomeMapSettings);
                    #endregion

                    payroll.AdditionalMessage = additionalIncomes?.Count > 0 || additionalOutcomes?.Count > 0 || removeIncomes?.Count > 0 || removeOutcomes?.Count > 0
                        ? "มีการแก้ไขรายการเงินเพิ่ม/เงินหัก อาจมีบางรายการถูกเพิ่มหรือลบออกไป โปรดตรวจทานอีกครั้ง"
                        : "";
                }
            }

            return payroll;
        }

        public async Task<IEnumerable<PayrollHistoryViewModel>> GetPayrollHistory(int id)
        {
            var result = await _payrollRepository
                .GetAll()
                .Where(x => x.Id == id)
                .ProjectTo<PayrollHistoryViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync();
            return result;
        }

        public async Task<IEnumerable<PayrollViewModel>> GetPayrolls()
        {
            var userId = _identityService.GetUserId();

            var payrolls = await _payrollRepository
                .GetAll()
                .Where(x => x.UserId == userId)
                .OrderByDescending(x => x.Code)
                    .ThenByDescending(x => x.CreatedDate)
                .ProjectTo<PayrollViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync();

            return payrolls;
        }

        public async Task PayPayroll(int id, PayPayrollViewModel payPayrollViewModel)
        {
            var payroll = await _payrollRepository
                .AllIncluding(x => x.PayrollEmployees)
                .FirstOrDefaultAsync(x => x.Id == id) ?? throw new InvalidOperationException("ไม่พบการจ่ายเงินเดือนนี้");
            payroll.Status = PayrollStatus.Pay;
            payroll.PayDate = payPayrollViewModel.PayDate;
            payroll.PayMethod = payPayrollViewModel.PayMethod;

            var employeeIds = payroll.PayrollEmployees.Select(x => x.EmployeeId).Distinct().ToList();
            var employee = await _employeeRepository.GetAll().Where(x => employeeIds.Contains(x.Id))
                .Select(x => new Employee { Id = x.Id, Bank = x.Bank, BankAccount = x.BankAccount })
                .AsNoTracking()
                .ToDictionaryAsync(x => x.Id);

            payroll.PayrollEmployees = payroll.PayrollEmployees.OrderBy(x => x.Employee.Code).ToList();
            Parallel.ForEach(payroll.PayrollEmployees, (item) =>
            {
                if (employee.TryGetValue(item.EmployeeId, out var currentEmployee))
                {
                    item.Bank = currentEmployee.Bank;
                    item.BankAccount = currentEmployee.BankAccount;
                }
            });

            var payrollHistory = new PayrollHistory
            {
                UserId = _identityService.GetUserId(),
                PayrollId = 0,
                Name = PayrollHistoryKeys.Pay,
                Description = "",
                CreatedDate = DateTime.Now,
            };
            payroll.PayrollHistories.Add(payrollHistory);

            await _unitOfWork.CommitAsync();
        }

        public async Task SendEmailSlip(SendEmailSlipViewModel sendEmailSlipViewModel)
        {
            var slips = new List<EmployeeSlipReportViewModel>();
            foreach (var employeeId in sendEmailSlipViewModel.EmployeeIds)
            {
                var slip = await _reportService
                    .GetEmployeeSlipReport(employeeId.ToString(), sendEmailSlipViewModel.PayrollId.ToString());
                slips.Add(slip);
            }

            var autoEmails = new ConcurrentBag<AutoEmail>();
            int userId = _identityService.GetUserId();
            Parallel.ForEach(slips, slip =>
           {
               var maxIncomeOutcomeRows = Math.Max(
                   slip.PayrollEmployeeIncomes?.Count ?? 0,
                   slip.PayrollEmployeeOutcomes?.Count ?? 0
               );

               var tableIncomeOutcome = new StringBuilder();
               for (int i = 0; i < maxIncomeOutcomeRows; i++)
               {
                   var income = i < slip.PayrollEmployeeIncomes?.Count ? slip.PayrollEmployeeIncomes?[i] : null;
                   var outcome = i < slip.PayrollEmployeeOutcomes?.Count ? slip.PayrollEmployeeOutcomes?[i] : null;
                   tableIncomeOutcome.AppendLine($@"
                    <tr style=""{(income is null ? "border-bottom: none;" : "")}"">
                        <td colspan=""2"" style=""border-left: 1px solid #000; border-right: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;"">{income?.Name}</td>
                        <td colspan=""1"" style=""border-left: 1px solid #000; text-align: right; border-right: 1px solid #000; padding: 8px; width: 16.66%;"">{income?.Amount.ToString(CurrencyKeys.Format)}</td>
                        <td colspan=""2"" style=""border-left: 1px solid #000; border-right: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;"">{outcome?.Name}</td>
                        <td colspan=""1"" style=""border-left: 1px solid #000; text-align: right; border-right: 1px solid #000; padding: 8px; width: 16.66%;"">{outcome?.Amount.ToString(CurrencyKeys.Format)}</td>
                    </tr>");
               }

               var emailBody = $@"
                        <div style=""margin: 24px; font-size:large;"">
                            <div style=""display: flex; justify-content: space-between;"">
                                <div style=""width: 75%;"">
                                    <div><h3><b>1988 Cafe</b></h3></div>
                                    <div><b>ข้อมูลพนักงาน</b></div>
                                    <div style=""display: flex;"">
                                        <span style=""flex-basis: 125px"">ชื่อ-นามสกุล (รหัส):</span>
                                        <span>[FirstName] [LastName] ([EmployeeCode])</span>
                                    </div>
                                    <div style=""display: flex;"">
                                        <span style=""flex-basis: 125px"">ตำแหน่ง:</span>
                                        <span>[PositionName]</span>
                                    </div>
                                    <div style=""display: flex;"">
                                        <span style=""flex-basis: 125px"">รับเงินโดย:</span>
                                        <span>[Bank] - [BankAccount]</span>
                                    </div>
                                </div>
                                <div style=""width: 25%; text-align: right;"">
                                    <div style=""text-align: right;""><h3><b>สลิปเงินเดือน/PaySlip</b></h3></div>
                                    <div style=""text-align: right;""><b>รอบเงินเดือน</b></div>
                                    <div>
                                        <span>ประจำเดือน:</span>
                                        <span>[PayrollDate]</span>
                                    </div>
                                    <div>
                                        <span>วันที่ชำระเงินเดือน:</span>
                                        <span>[PayDate]</span>
                                    </div>
                                </div>
                            </div>

                            <div style=""padding-top: 8px;"">
                                <h4 style=""margin-bottom: 4px;""><b>รายการเงินเดือน {slip.PayrollDate:yyyy/MM}</b></h4>
                            </div>

                            <div>
                                <table style=""border-collapse: collapse; width: 100%;"">
                                    <thead>
                                        <tr>
                                            <th colspan=""2"" style=""border: 1px solid #000; padding: 8px; background-color: #f2f2f2; text-align: center;"">รายได้</th>
                                            <th colspan=""1"" style=""border: 1px solid #000; padding: 8px; background-color: #f2f2f2; text-align: center;"">จำนวนเงิน(บาท)</th>
                                            <th colspan=""2"" style=""border: 1px solid #000; padding: 8px; background-color: #f2f2f2; text-align: center;"">รายการหัก</th>
                                            <th colspan=""1"" style=""border: 1px solid #000; padding: 8px; background-color: #f2f2f2; text-align: center;"">จำนวนเงิน(บาท)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {tableIncomeOutcome}
                                        <tr>
                                            <td colspan=""2"" style=""border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;"">รายได้รวม</td>
                                            <td colspan=""1"" style=""border: 1px solid #000; text-align: right; padding: 8px; width: 16.66%;"">[TotalIncome]</td>
                                            <td colspan=""2"" style=""border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;"">รายหักรวม</td>
                                            <td colspan=""1"" style=""border: 1px solid #000; text-align: right; padding: 8px; width: 16.66%;"">[TotalOutcome]</td>
                                        </tr>
                                        <tr>
                                            <td colspan=""3"" style=""border: none;""></td>
                                            <td colspan=""2"" style=""border: 1px solid #000; padding: 8px; text-align: center; background-color: #f2f2f2; width: 33.33%;""><b>เงินรับสุทธิ</b></td>
                                            <td colspan=""1"" style=""border: 1px solid #000; text-align: right; padding: 8px; background-color: #f2f2f2; width: 16.66%;""><b>[Total]</b></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div style=""padding-top: 8px;"">
                                <h4 style=""margin-bottom: 4px;""><b>รวมเงินได้ทั้งปี {slip.PayrollDate.Year}</b></h4>
                            </div>

                            <div>
                                <table style=""border-collapse: collapse; width: 100%;"">
                                    <thead>
                                        <tr>
                                            <th style=""border: 1px solid #000; padding: 8px; background-color: #f2f2f2; text-align: center; width: 25%;"">เงินได้สะสม</th>
                                            <th style=""border: 1px solid #000; padding: 8px; background-color: #f2f2f2; text-align: center; width: 25%;"">ภาษีหัก ณ ที่จ่ายสะสม</th>
                                            <th style=""border: 1px solid #000; padding: 8px; background-color: #f2f2f2; text-align: center; width: 25%;"">เงินประกันสังคมสะสม</th>
                                            <th style=""border: 1px solid #000; padding: 8px; background-color: #f2f2f2; text-align: center; width: 25%;"">ลายเซ็น</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style=""border: 1px solid #000; padding: 8px; text-align: center;"">[SummaryTotalIncome]</td>
                                            <td style=""border: 1px solid #000; padding: 8px; text-align: center;"">[SummaryTotalTax]</td>
                                            <td style=""border: 1px solid #000; padding: 8px; text-align: center;"">[SummaryTotalSocialSecurityFund]</td>
                                            <td style=""border: 1px solid #000; padding: 8px; text-align: center;""></td>
                                        </tr>
                                    </tbody>
                                </table>        
                            </div>
                        </div>";
               ReplaceEmailTemplateKeys(slip, ref emailBody);

               autoEmails.Add(new AutoEmail
               {
                   UserId = userId,
                   To = slip.EmployeeEmail,
                   Subject = "Payroll Employee Slip",
                   Body = emailBody,
                   EmailTemplateType = (int)EmailTemplateType.EmployeeSlip
               });
           });

            _autoEmailRepository.AddRange(autoEmails);
            await _unitOfWork.CommitAsync();
        }

        private static void ReplaceEmailTemplateKeys(EmployeeSlipReportViewModel slip, ref string message)
        {
            message = message.Replace(EmailTemplateKeys.FirstName, slip.FirstName);
            message = message.Replace(EmailTemplateKeys.LastName, slip.LastName);
            message = message.Replace(EmailTemplateKeys.EmployeeCode, slip.EmployeeCode);
            message = message.Replace(EmailTemplateKeys.PositionName, slip.PositionName);
            message = message.Replace(EmailTemplateKeys.Bank, slip.Bank);
            message = message.Replace(EmailTemplateKeys.BankAccount, slip.BankAccount);
            message = message.Replace(EmailTemplateKeys.PayrollDate, DateTimeUtils.GetThaiMonthYear(slip.PayrollDate));
            message = message.Replace(EmailTemplateKeys.PayDate, slip.PayDate.HasValue ? slip.PayDate.Value.ToString("dd/MM/yyyy") : null);
            message = message.Replace(EmailTemplateKeys.TotalIncome, slip.TotalIncome.ToString(CurrencyKeys.Format));
            message = message.Replace(EmailTemplateKeys.TotalOutcome, slip.TotalOutcome.ToString(CurrencyKeys.Format));
            message = message.Replace(EmailTemplateKeys.Total, slip.Total.ToString(CurrencyKeys.Format));
            message = message.Replace(EmailTemplateKeys.SummaryTotalIncome, slip.Summary.TotalIncome.ToString(CurrencyKeys.Format));
            message = message.Replace(EmailTemplateKeys.SummaryTotalTax, slip.Summary.TotalTax.ToString(CurrencyKeys.Format));
            message = message.Replace(EmailTemplateKeys.SummaryTotalSocialSecurityFund, slip.Summary.TotalSocialSecurityFund.ToString(CurrencyKeys.Format));
        }
    }
}
