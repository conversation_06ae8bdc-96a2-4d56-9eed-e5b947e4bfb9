﻿using Microsoft.AspNetCore.Http;
using payroll_api.utility.ViewModel;

namespace payroll_api.Infrastructure.Service
{
    public class IdentityService : IIdentityService
    {
        private readonly IHttpContextAccessor _context;

        public IdentityService(IHttpContextAccessor httpContextAccessor)
        {
            _context = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        public int GetUserId()
        {
            var user = _context?.HttpContext?.Items["User"] as UserViewModel;
            return user?.Id ?? 0;
        }
    }
}
