﻿using payroll_api.utility.ViewModel;

namespace payroll_api.service
{
    public interface IPayrollService {
        Task<int> CreatePayroll(CreatePayrollViewModel createPayrollViewModel);
        Task<PayrollDetailViewModel?> GetPayroll(int id, bool copy);
        Task<IEnumerable<PayrollViewModel>> GetPayrolls();
        Task CancelPayroll(int id);
        Task<IEnumerable<PayrollHistoryViewModel>> GetPayrollHistory(int id);
        Task SendEmailSlip(SendEmailSlipViewModel sendEmailSlipViewModel);
        Task PayPayroll(int id, PayPayrollViewModel payPayrollViewModel);
        Task Approve(int id);
        Task<string> GenerateCode(int startMonth = 0);
        byte[] DownloadImportPayrollExcel();
        Task<byte[]> DownloadPayrollExcelReport(int payrollId);
    }
}
