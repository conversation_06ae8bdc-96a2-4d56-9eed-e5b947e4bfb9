using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.service
{
    public interface IIncomeService
    {
        Task<IEnumerable<IncomeViewModel>> GetIncomes(bool? active = null);
        Task<PageResponseViewModel<List<IncomeViewModel>>> GetIncomePage(PageFilterViewModel pageFilter);
        Task CreateIncome(IncomeViewModel incomeViewModel);
        Task UpdateIncome(int ID, UpdateIncomeViewModel incomeViewModel);
        Task GenerateSystemIncomes(int userId);
        Task UpdateActiveIncome(int id, UpdateIncomeActiveViewModel updateIncomeActiveViewModel);
    }
}
