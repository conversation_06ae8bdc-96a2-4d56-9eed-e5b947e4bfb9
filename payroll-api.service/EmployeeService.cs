﻿using AutoMapper;
using Dapper;
using Microsoft.EntityFrameworkCore;
using payroll_api.data;
using payroll_api.data.Extensions;
using payroll_api.data.Infrastructure;
using payroll_api.data.Repository;
using payroll_api.domain;
using payroll_api.Infrastructure.Service;
using payroll_api.report.Models;
using payroll_api.service.Infrastructure.Helper;
using payroll_api.utility;
using payroll_api.utility.Enum;
using payroll_api.utility.Filter;
using payroll_api.utility.Helper;
using payroll_api.utility.ViewModel;

namespace payroll_api.service
{
    public class EmployeeService : IEmployeeService
    {
        private readonly IEmployeeRepository _employeeRepository;
        private readonly IDepartmentRepository _departmentRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IIdentityService _identityService;
        private readonly DapperContext _dapperContext;

        public EmployeeService(
            IEmployeeRepository employeeRepository,
            IDepartmentRepository departmentRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IIdentityService identityService,
            DapperContext dapperContext
        )
        {
            _employeeRepository = employeeRepository ?? throw new ArgumentNullException(nameof(employeeRepository));
            _departmentRepository = departmentRepository ?? throw new ArgumentNullException(nameof(departmentRepository));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
        }

        public async Task<IEnumerable<EmployeeViewModel>> GetEmployees()
        {
            using var conn = _dapperContext.CreateConnection();
            var userId = _identityService.GetUserId();

            var sql = @"
                SELECT 
                    e.Id, e.FirstName, e.LastName, e.Salary, e.Code, e.IsSocialSecurity,
                    b.Name AS BranchName, b.Id AS BranchId  
                FROM 
                    Employees e WITH (NOLOCK)
                LEFT JOIN 
                    Branchs b WITH (NOLOCK) ON b.Id = e.BranchId
                WHERE 
                    e.Active = 1 AND e.UserId = @UserId
            ";
            var result = (await conn.QueryAsync<EmployeeViewModel>(sql, new { UserId = userId })).ToList();

            if (result.Count != 0)
            {
                Parallel.ForEach(result, (item) =>
                {
                    var annualSalary = item.Salary * 12;
                    item.Tax = TaxCalculator.CalculateTax(annualSalary);
                    item.SocialSecurityFund = item.IsSocialSecurity ? SocialFundCalculator.CalculateSocialFund(item.Salary) : 0;
                });
            }

            return result;
        }

        public async Task<EmployeeDetailViewModel?> GetEmployeeDetail(int id)
        {
            using var connection = _dapperContext.CreateConnection();
            var sql = @"
                SELECT 
                    e.Id, 
                    e.FirstName, 
                    e.LastName, 
                    e.Email, 
                    e.Phone, 
                    e.DepartmentId, 
                    e.PositionId, 
                    e.BranchId, 
                    e.Salary, 
                    e.StartDate,
                    e.EmployeeType, 
                    e.IsSocialSecurity, 
                    e.Bank, 
                    e.BankAccount,
                    e.Active,
                    ISNULL(ea.EmployeeId, 0) AS EmployeeId,
                    ea.Address,
                    ea.District,
                    ea.Province,
                    ea.SubDistrict,
                    ea.ZipCode
                FROM Employees e WITH (NOLOCK)
                LEFT JOIN EmployeeAddresses ea on ea.EmployeeId = e.id
                WHERE e.Id = @Id
            ";
            return (await connection.QueryAsync<EmployeeDetailViewModel, AddressViewModel, EmployeeDetailViewModel>(
                sql,
                (employee, address) =>
                {
                    employee.Address = address;
                    return employee;
                },
                new { Id = id },
                splitOn: "EmployeeId"
            )).Distinct().FirstOrDefault();
        }

        public async Task UpdateEmployee(int id, UpdateEmployeeViewModel updateEmployeeViewModel)
        {
            var employee = _employeeRepository.AllIncluding(e => e.Address).FirstOrDefault(e => e.Id == id)
                ?? throw new ApplicationException("Employee not found.");

            if (employee.DepartmentId != updateEmployeeViewModel.DepartmentId)
            {
                var userId = _identityService.GetUserId();
                employee.Code = await GetRunningNumber(updateEmployeeViewModel.DepartmentId, userId);
            }

            _mapper.Map(updateEmployeeViewModel, employee);
            _employeeRepository.Update(employee);
            await _unitOfWork.CommitAsync();
        }

        public async Task CreateEmployee(CreateEmployeeViewModel employeeViewModel)
        {
            var userId = _identityService.GetUserId();
            var employee = _mapper.Map<Employee>(employeeViewModel);
            employee.Active = true;
            employee.UserId = userId;

            // Generate Employee Code
            employee.Code = await GetRunningNumber(employee.DepartmentId, userId);

            _employeeRepository.Add(employee);

            await _unitOfWork.CommitAsync();
        }

        public async Task<PageResponseViewModel<List<EmployeeViewModel>>> GetEmployeePage(EmployeeFilterViewModel pageFilter)
        {
            var userId = _identityService.GetUserId();
            using var conn = _dapperContext.CreateConnection();

            var baseSql = @"
                SELECT
                    e.Id,
                    e.FirstName + ' ' + e.LastName AS FullName,
                    e.Code,
                    e.PositionId,
                    e.Salary,
                    e.UserId,
                    e.EmployeeType,
                    CASE 
                        WHEN e.EmployeeType = 1 THEN 'Full Time'
                        WHEN e.EmployeeType = 2 THEN 'Part Time'
                        ELSE ''
                    END AS EmployeeTypeName,
                    e.Active,
                    d.Name AS Department,
                    ISNULL(p.Name, '') AS Position,
                    ISNULL(b.Name, '') AS Branch
                FROM Employees e WITH (NOLOCK)
                    INNER JOIN Departments d WITH (NOLOCK) ON d.Id = e.DepartmentId
                    LEFT JOIN Positions p WITH (NOLOCK) ON p.Id = e.PositionId
                    LEFT JOIN Branchs b WITH (NOLOCK) ON b.Id = e.BranchId
            ";
            var sqlFilter = "WHERE e.UserId = @UserId AND e.Active = @Active";

            if (!string.IsNullOrWhiteSpace(pageFilter.SearchTerm))
            {
                pageFilter.SearchTerm += "%";
                sqlFilter = sqlFilter + " AND " + "(e.Code LIKE @SearchTerm OR e.FirstName LIKE @SearchTerm OR e.LastName LIKE @SearchTerm OR e.FirstName + ' ' + e.LastName LIKE @SearchTerm)";
            }

            if (pageFilter.DepartmentId.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.DepartmentId = @DepartmentId";

            if (pageFilter.BranchId.HasValue)
                sqlFilter = sqlFilter + " AND " + (pageFilter.BranchId.Value == -1 ? "e.BranchId IS NULL" : "e.BranchId = @BranchId");

            if (pageFilter.EmployeeType.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.EmployeeType = @EmployeeType";

            if (pageFilter.PositionId.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.PositionId = @PositionId";

            if (pageFilter.MinSalary.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.Salary >= @MinSalary";

            if (pageFilter.MaxSalary.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.Salary <= @MaxSalary";

            var sqlCount = @"SELECT COUNT(1) FROM Employees e WITH (NOLOCK) " + sqlFilter;
            var total = await conn.ExecuteScalarAsync<int>(
                sqlCount,
                new
                {
                    UserId = userId,
                    pageFilter.SearchTerm,
                    pageFilter.DepartmentId,
                    pageFilter.BranchId,
                    pageFilter.PositionId,
                    pageFilter.EmployeeType,
                    pageFilter.MinSalary,
                    pageFilter.MaxSalary,
                    Active = pageFilter.IsActive
                }
            );

            sqlFilter += pageFilter.GetSortQuery("ORDER BY e.Code");
            var sql = baseSql + sqlFilter + " OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY";
            var pageData = (await conn.QueryAsync<EmployeeViewModel>(
                sql,
                new
                {
                    UserId = userId,
                    pageFilter.SearchTerm,
                    pageFilter.DepartmentId,
                    pageFilter.BranchId,
                    Offset = pageFilter.PageIndex * pageFilter.PageSize,
                    Limit = pageFilter.PageSize,
                    pageFilter.PositionId,
                    pageFilter.EmployeeType,
                    pageFilter.MinSalary,
                    pageFilter.MaxSalary,
                    Active = pageFilter.IsActive
                })
            )
            .ToList();

            var result = PageHelper.CreatePagedReponse(
                pageData,
                pageFilter,
                total
            );

            return result;
        }

        public async Task<ReportEmployeeModel> GetEmployeeReport(int userId, EmployeeFilterViewModel pageFilter)
        {
            using var connection = _dapperContext.CreateConnection();

            var sql = @"
                SELECT
                    d.Name,
                    e.Id AS EmployeeId,
                    e.FirstName + ' ' + e.LastName AS FullName,
                    e.Code,
                    e.Salary,
                    b.Name AS Branch,
                    ISNULL(p.Name, '') AS Position
                FROM Departments d WITH (NOLOCK)
                INNER JOIN Employees e WITH (NOLOCK) ON e.DepartmentId = d.Id
                LEFT JOIN Positions p WITH (NOLOCK) ON e.PositionId = p.Id
                LEFT JOIN Branchs b WITH (NOLOCK) ON b.Id = e.BranchId
            ";

            var sqlFilter = "WHERE e.UserId = @UserId AND e.Active = @Active";

            if (!string.IsNullOrWhiteSpace(pageFilter.SearchTerm))
            {
                pageFilter.SearchTerm += "%";
                sqlFilter = sqlFilter + " AND " + "(e.Code LIKE @SearchTerm OR e.FirstName LIKE @SearchTerm OR e.LastName LIKE @SearchTerm OR e.FirstName + ' ' + e.LastName LIKE @SearchTerm)";
            }

            if (pageFilter.DepartmentId.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.DepartmentId = @DepartmentId";

            if (pageFilter.BranchId.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.BranchId = @BranchId";

            if (pageFilter.EmployeeType.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.EmployeeType = @EmployeeType";

            if (pageFilter.PositionId.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.PositionId = @PositionId";

            if (pageFilter.MinSalary.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.Salary >= @MinSalary";

            if (pageFilter.MaxSalary.HasValue)
                sqlFilter = sqlFilter + " AND " + "e.Salary <= @MaxSalary";

            sqlFilter += pageFilter.GetSortQuery("ORDER BY d.Name, e.Code");
            sql += sqlFilter;

            var departmentDictionary = new Dictionary<string, DepartmentReportModel>();

            var result = await connection.QueryAsync<DepartmentReportModel, EmployeeReportModel, DepartmentReportModel>(
                sql: sql,
                param: new
                {
                    UserId = userId,
                    pageFilter.SearchTerm,
                    pageFilter.DepartmentId,
                    pageFilter.BranchId,
                    pageFilter.PositionId,
                    pageFilter.EmployeeType,
                    pageFilter.MinSalary,
                    pageFilter.MaxSalary,
                    Active = pageFilter.IsActive
                },
                map: (department, employee) =>
                {
                    if (!departmentDictionary.TryGetValue(department.Name, out var dep))
                    {
                        dep = department;
                        dep.Employee = new List<EmployeeReportModel>();
                        departmentDictionary.Add(dep.Name, dep);
                    }

                    dep.Employee.Add(employee);
                    return dep;
                },
                splitOn: "EmployeeId"
            );

            return new ReportEmployeeModel { Departments = result.Distinct().ToList() };
        }

        public async Task UpdateEmployee(int id, CreateEmployeeViewModel createEmployeeViewModel)
        {
            var employee = await _employeeRepository.AllIncluding(e => e.Address).FirstOrDefaultAsync(e => e.Id == id);
            _mapper.Map(createEmployeeViewModel, employee);
            _employeeRepository.Update(employee);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeleteEmployee(int id)
        {
            var employee = await _employeeRepository.AllIncluding(e => e.Address).FirstOrDefaultAsync(e => e.Id == id);
            employee.Active = false;
            _employeeRepository.Update(employee);
            await _unitOfWork.CommitAsync();
        }

        public async Task<Dictionary<string, int>> GetRunningNumberByDepartmentCode(int userId)
        {
            var result = await _employeeRepository
                .AllIncluding(x => x.Department)
                .Where(x => x.UserId == userId)
                .GroupBy(x => x.Department.Code)
                .Select(x => new
                {
                    DepartmentCode = x.Key,
                    RunningNumber = x.Max(x => x.Code)
                })
                .ToDictionaryAsync(x => x.DepartmentCode, x => Convert.ToInt32(x.RunningNumber.Replace(x.DepartmentCode, "")));
            return result ?? new Dictionary<string, int>();
        }

        public async Task<string> GetRunningNumber(int departmentId, int userId)
        {
            var empCode = await _employeeRepository.GetAll()
                .Where(x => x.UserId == userId && x.DepartmentId == departmentId)
                .MaxAsync(x => x.Code);

            if (empCode is null)
            {
                var depCode = _departmentRepository.GetCode(departmentId, userId);
                return $"{depCode}0001";
            }

            var (code, runningNunber) = DataManager.ExtractCode(empCode);
            runningNunber++;
            return $"{code}{runningNunber:D4}";
        }

        public async Task<PageResponseViewModel<List<EmployeeSalaryHistoryViewModel>>> GetEmployeeSalaryHistory(EmployeeSalaryHistoryFilterViewModel filter)
        {
            using var connection = _dapperContext.CreateConnection();

            var sqlFilter = @"
                WHERE pe.EmployeeId = @EmployeeId AND p.Status = @PayrollStatus
            ";
            var sql = @$"
                SELECT COUNT(1) 
                FROM PayrollEmployees pe
                INNER JOIN 
                    Payrolls p ON p.Id = pe.PayrollId
                {sqlFilter};

                SELECT 
                    CONVERT(VARCHAR, CAST(p.PayrollDate AS DATE), 23) AS Date,
                    pe.TotalIncome,
                    pe.TotalOutcome,
                    pe.Total,
                    p.Id AS PayrollId,
                    pe.EmployeeId
                FROM 
                    PayrollEmployees pe 
                INNER JOIN 
                    Payrolls p ON p.Id = pe.PayrollId
                {sqlFilter}
            ";

            var parameters = new
            {
                Offset = filter.PageIndex * filter.PageSize,
                Limit = filter.PageSize,
                filter.EmployeeId,
                PayrollStatus = PayrollStatus.Pay
            };

            sql = sql + filter.GetSortQuery("ORDER BY Date") + " OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY";

            using var multi = await connection.QueryMultipleAsync(sql, parameters);
            var total = await multi.ReadFirstAsync<int>();
            var pageData = (await multi.ReadAsync<EmployeeSalaryHistoryViewModel>()).ToList();
            var result = PageHelper.CreatePagedReponse(
                pageData,
                filter,
                total
            );

            return result;
        }

        public async Task<EmployeeFullDetailViewModel> GetEmployeeFullDetail(int id)
        {
            var userId = _identityService.GetUserId();
            using var connection = _dapperContext.CreateConnection();

            var sql = @"
                select 
                    e.Id,
                    e.FirstName + ' ' + e.LastName as FullName,
                    e.Email,
                    e.Phone,
                    e.Code,
                    e.Salary,
                    e.StartDate,
                    e.IsSocialSecurity,
                    e.Bank,
                    e.BankAccount,
                    e.EmployeeType,
                    e.Active,
                    CASE 
                        WHEN e.EmployeeType = 1 THEN 'Full Time'
                        WHEN e.EmployeeType = 2 THEN 'Part Time'
                        ELSE '' 
                    END as EmployeeTypeName,
                    d.Name as Department,
                    ISNULL(p.Name, '') as Position,
                    ISNULL(b.Name, '') as Branch,
                    ea.Address,
                    ea.District,
                    ea.Province,
                    ea.SubDistrict,
                    ea.ZipCode
                from Employees e
                    inner join Departments d on d.Id = e.DepartmentId
                    left join EmployeeAddresses ea on ea.EmployeeId = e.id
                    left join Positions p on p.Id = e.PositionId
                    left join Branchs b on b.Id = e.BranchId
                where e.Id = @Id and e.UserId = @UserId
            ";

            var result = await connection.QueryAsync<EmployeeFullDetailViewModel, AddressViewModel, EmployeeFullDetailViewModel>(
                        sql,
                        (e, ea) =>
                        {
                            e.Address = ea;
                            return e;
                        },
                        new { Id = id, UserId = userId },
                        splitOn: "Address"
                    );
            return result.FirstOrDefault();
        }

        public List<EmployeeViewModel> GetDuplicatedEmployee(List<EmployeeViewModel> employeeToValidate)
        {
            var conn = _dapperContext.CreateConnection();
            var sql = @"
                SELECT Id, FirstName, LastName
                FROM Employees WITH (NOLOCK)
                WHERE UserId = @UserId
            ";
            var existingEmployees = conn.Query<EmployeeViewModel>(sql, new { UserId = _identityService.GetUserId() }).ToList();

            var duplicatedEmployees = (
                from emp in existingEmployees
                join inputEmp in employeeToValidate
                on new { emp.FirstName, emp.LastName } equals new { inputEmp.FirstName, inputEmp.LastName }
                select inputEmp
            ).ToList();

            return duplicatedEmployees;
        }

        public async Task UpdateActiveEmployee(int id, UpdateEmployeeActiveViewModel updateEmployeeViewModel)
        {
            var employee = _employeeRepository.GetAll().FirstOrDefault(e => e.Id == id) ?? throw new ApplicationException("Employee not found.");
            employee.Active = updateEmployeeViewModel.Active;
            _employeeRepository.Update(employee);
            await _unitOfWork.CommitAsync();
        }
    }
}
