using payroll_api.data.Repository;
using payroll_api.Infrastructure.Service;
using payroll_api.utility.ViewModel;

namespace payroll_api.service
{
    public class NotificationService : INotificationService
    {
        private readonly IPayrollRepository _payrollRepository;
        private readonly IIdentityService _identityService;
        private readonly IUserRepository _userRepository;

        public NotificationService(
            IPayrollRepository payrollRepository,
            IIdentityService identityService,
            IUserRepository userRepository
        )
        {
            _payrollRepository = payrollRepository ?? throw new ArgumentNullException(nameof(payrollRepository));
            _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        }

        public async Task<IEnumerable<NotificationViewModel>> GetNotifications()
        {
            var result = new List<NotificationViewModel>();
            var userId = _identityService.GetUserId();

            await GetUserExpiredNotification(result, userId);
            await GetCreatePayrollNotification(result, userId);

            return result;
        }

        private async Task GetCreatePayrollNotification(List<NotificationViewModel> result, int userId)
        {
            var dateUtc = DateTime.UtcNow;
            var isExistPayrollInCurrentMonth = await _payrollRepository.IsExistPayrollInCurrentMonth(dateUtc.Month, dateUtc.Year, userId);
            if (!isExistPayrollInCurrentMonth)
            {
                result.Add(new NotificationViewModel
                {
                    Title = "แจ้งเตือน",
                    Description = "คุณยังไม่ได้ทำการจ่ายเงินเดือนในเดือนนี้",
                });
            }
        }

        private async Task GetUserExpiredNotification(List<NotificationViewModel> result, int userId)
        {
            var user = await _userRepository.GetUser(userId);
            int expiredUserInDays = user.ExpireDate.Date.Subtract(DateTime.Now.Date).Days;
            if (expiredUserInDays <= 7)
            {
                result.Add(new NotificationViewModel
                {
                    Title = "แจ้งเตือน",
                    Description = $"โปรแกรมจะหมดอายุในอีก {expiredUserInDays} วัน",
                });
            }
        }
    }
}
