﻿using System.Data.Entity;
using AutoMapper;
using Dapper;
using Microsoft.EntityFrameworkCore;
using payroll_api.data;
using payroll_api.data.Infrastructure;
using payroll_api.data.Repository;
using payroll_api.domain.Settings;
using payroll_api.Infrastructure.Service;
using payroll_api.utility.Constant;
using payroll_api.utility.ViewModel;

namespace payroll_api.service
{
    public class SettingService : ISettingService
    {
        private readonly ISettingRepository _settingRepository;
        private readonly ISettingImageRepository _settingImageRepository;
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private readonly DapperContext _dapperContext;
        private readonly IIdentityService _identityService;

        public SettingService(
            ISettingRepository settingRepository,
            ISettingImageRepository settingImageRepository,
            IMapper mapper,
            IUnitOfWork unitOfWork,
            DapperContext dapperContext,
            IIdentityService identityService)
        {
            _settingRepository = settingRepository ?? throw new ArgumentNullException(nameof(settingRepository));
            _settingImageRepository = settingImageRepository ?? throw new ArgumentNullException(nameof(settingImageRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
            _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
        }

        public async Task<AppSettingViewModel> GetReadonlySettings(int userId)
        {
            return await _settingRepository.GetAllSettings(userId);
        }

        public async Task SaveSettings(UpdateSettingViewModel settings)
        {
            var userId = _identityService.GetUserId();
            var settingNameList = settings.Settings.Select(x => x.Value.Name).Distinct().ToList();
            var settingList = settings.Settings.Select(x => x.Value).ToList();
            var settingsDB = await _settingRepository.GetAll()
                .Where(x => x.UserId == userId && settingNameList.Contains(x.Name))
                .ToListAsync();

            var isUpdate = false;

            // Update Exist Options
            settingsDB.ForEach(item =>
            {
                var updateSettingValue = settingList
                    .FirstOrDefault(x => x.Name == item.Name && x.Value != item.Value);
                if (updateSettingValue != null)
                {
                    item.Value = updateSettingValue.Value;
                    isUpdate = true;
                }
            });

            if (isUpdate)
                await _unitOfWork.CommitAsync();
        }

        public async Task<AllSettingViewModel> GetSettings(int userId)
        {
            using var conn = _dapperContext.CreateConnection();
            var sqlSetting = @"SELECT Id,Name,Value 
                        FROM Settings WITH (NOLOCK)
                        WHERE UserId = @UserId;";
            var sqlSettingImage = @"SELECT Id,Content,Name FROM SettingImages WITH (NOLOCK) WHERE UserId = @UserId;";
            var sql = sqlSetting + sqlSettingImage;
            using var multi = await conn.QueryMultipleAsync(sql, new { UserId = userId });
            var setting = (await multi.ReadAsync<SettingViewModel>()).ToDictionary(x => x.Name);
            var settingImage = (await multi.ReadAsync<SettingImageViewModel>()).ToDictionary(x => x.Name);

            return new AllSettingViewModel
            {
                Settings = setting,
                SettingImages = settingImage
            };
        }

        public async Task<AppSettingViewModel> GetOptionByNameAsync(int userId, params string[] names)
        {
            var result = await _settingRepository.GetSettingByNames(userId, names);
            return result;
        }
        public T GetOptionByName<T>(int userId, string name, T defaultValue = default)
        {
            try
            {
                using var conn = _dapperContext.CreateConnection();
                var sql = @"
                    SELECT TOP 1 Value
                    FROM Settings s WITH (NOLOCK)
                    WHERE UserId = @UserId AND Name = @Name
                ";
                var result = conn.QueryFirstOrDefault<T>(sql, new { UserId = userId, Name = name });
                return result is not null ? result : defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        public async Task UploadImage(SettingImageViewModel settingImageViewModel)
        {
            var settingImage = await _settingImageRepository
                .FindBy(x =>
                    x.UserId == settingImageViewModel.UserId &&
                    x.Name == settingImageViewModel.Name
                )
                .FirstOrDefaultAsync();

            if (settingImage is null)
            {
                settingImage = _mapper.Map<SettingImage>(settingImageViewModel);
                _settingImageRepository.Add(settingImage);
            }
            else
            {
                settingImage.Content = settingImageViewModel.Content;
                _settingImageRepository.Update(settingImage);
            }
            await _unitOfWork.CommitAsync();
        }

        public async Task RemoveImage(int id)
        {
            var settingImage = new SettingImage { Id = id };
            _settingImageRepository.Delete(settingImage);
            await _unitOfWork.CommitAsync();
        }

        public T GetOptionImageByName<T>(int userId, string name, T defaultValue = default)
        {
            try
            {
                using var conn = _dapperContext.CreateConnection();

                var sql = @"
                    SELECT TOP 1 Content
                    FROM SettingImages s WITH (NOLOCK)
                    WHERE UserId = @UserId AND Name = @Name
                ";
                var result = conn.QueryFirstOrDefault<T>(sql, new { UserId = userId, Name = name });
                return result is not null ? result : defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        public async Task<SettingContactViewModel> GetOptionCompanyInfo(int userId)
        {
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT Name, Value
                FROM Settings s WITH (NOLOCK)
                WHERE UserId = @UserId AND Name IN @Names
            ";
            var names = new List<string>() {
                SettingKey.CompanyName,
                SettingKey.Email,
                SettingKey.Phone,
                SettingKey.Address,
                SettingKey.District,
                SettingKey.SubDistrict,
                SettingKey.Province,
                SettingKey.Zipcode
            };
            var settings = (await conn.QueryAsync<SettingViewModel>(
                sql,
                new { UserId = userId, Names = names }
            )).ToDictionary(x => x.Name, x => x.Value);
            var result = new SettingContactViewModel();

            string Get(string name) => (settings.TryGetValue(name, out string? value) ? value : string.Empty) ?? string.Empty;

            result.CompanyName = Get(SettingKey.CompanyName);
            var email = Get(SettingKey.Email);
            var phone = Get(SettingKey.Phone);
            result.ContactInfo = $"{email} {phone}";
            var address = Get(SettingKey.Address);
            var district = Get(SettingKey.District);
            var subDistrict = Get(SettingKey.SubDistrict);
            var zipcode = Get(SettingKey.Zipcode);
            result.AddressInfo = $"{address} {subDistrict} {district}  {zipcode}";

            return result;
        }

        public async Task GenerateSystemSetting(int id)
        {
            var settings = new List<Setting>()
            {
                new() { Name = SettingKey.CompanyName, UserId = id, Type = SettingTypeKey.General },
                new() { Name = SettingKey.CompanyPassportID, UserId = id, Type = SettingTypeKey.General },
                new() { Name = SettingKey.Email, UserId = id, Type = SettingTypeKey.Contact },
                new() { Name = SettingKey.Phone, UserId = id, Type = SettingTypeKey.Contact },
                new() { Name = SettingKey.Fax, UserId = id, Type = SettingTypeKey.Contact },
                new() { Name = SettingKey.Website, UserId = id, Type = SettingTypeKey.Contact },
                new() { Name = SettingKey.EnableSocialSecurity, UserId = id, Type = SettingTypeKey.SocialSecurity },
                new() { Name = SettingKey.SocialSecurityPassportID, UserId = id, Type = SettingTypeKey.SocialSecurity },
                new() { Name = SettingKey.Address, UserId = id, Type = SettingTypeKey.Address },
                new() { Name = SettingKey.Zipcode, UserId = id, Type = SettingTypeKey.Address },
                new() { Name = SettingKey.District, UserId = id, Type = SettingTypeKey.Address },
                new() { Name = SettingKey.SubDistrict, UserId = id, Type = SettingTypeKey.Address },
                new() { Name = SettingKey.Province, UserId = id, Type = SettingTypeKey.Address },
                new() { Name = SettingKey.SalaryDueDate, UserId = id, Type = SettingTypeKey.Salary },
                new() { Name = SettingKey.Bank, UserId = id, Type = SettingTypeKey.Bank },
                new() { Name = SettingKey.BankAccount, UserId = id, Type = SettingTypeKey.Bank },
            };

            _settingRepository.AddRange(settings);
            await _unitOfWork.CommitAsync();
        }
    }
}
