using System.Data.Entity;
using Microsoft.Extensions.Logging;
using payroll_api.data.Infrastructure;
using payroll_api.data.Repository;
using payroll_api.email;
using payroll_api.utility.Enum;
using payroll_api.utility.Extensions;

namespace payroll_api.background_service.Services;

public class AutoEmailService : IAutoEmailService
{
    private readonly IAutoEmailRepository _autoEmailRepository;
    private readonly IEmailSender _emailSender;
    private readonly IUnitOfWork _unitOfWork;
    private readonly Serilog.ILogger _logger;

    public AutoEmailService(
        IAutoEmailRepository autoEmailRepository,
        IEmailSender emailSender,
        Serilog.ILogger logger,
        IUnitOfWork unitOfWork
    )
    {
        _autoEmailRepository = autoEmailRepository ?? throw new ArgumentNullException(nameof(autoEmailRepository));
        _emailSender = emailSender ?? throw new ArgumentNullException(nameof(emailSender));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _logger = logger?.ForContext("Email", true) ?? throw new ArgumentNullException(nameof(logger));
    }

    public Task SendEmailAsync() => Task.CompletedTask;
    public Task SendEmailByTemplate(EmailTemplateType emailTemplateType)
    {
        switch (emailTemplateType)
        {
            case EmailTemplateType.EmployeeSlip:
                return SendEmployeeSlipEmailAsync();
            default:
                return Task.CompletedTask;
        }
    }

    private async Task SendEmployeeSlipEmailAsync()
    {
        var autoEmails = await _autoEmailRepository.GetAll()
            .Where(x =>
                x.EmailTemplateType == (int)EmailTemplateType.EmployeeSlip &&
                x.SentDate == null
            )
            .ToListAsync();
        if (autoEmails.IsNullOrEmpty()) return;

        foreach (var autoEmail in autoEmails)
        {
            var message = new Message([autoEmail.To], autoEmail.Subject, autoEmail.Body);
            await _emailSender.SendEmailAsync(message);

            var sentDate = DateTime.UtcNow;
            autoEmail.SentDate = sentDate;
            _logger.Information(
                "{Method} - Payroll Slip Email sent to {Recipient} at {SentDate}",
                nameof(SendEmployeeSlipEmailAsync),
                autoEmail.To,
                sentDate
            );

            _autoEmailRepository.Update(autoEmail);
        }

        await _unitOfWork.CommitAsync();
    }
}