using AutoMapper;
using Dapper;
using payroll_api.data;
using payroll_api.data.Infrastructure;
using payroll_api.data.Repository;
using payroll_api.domain;
using payroll_api.Infrastructure.Service;
using payroll_api.service.Infrastructure.Helper;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.service
{
    public class BranchService : IBranchService
    {
        private readonly IMapper _mapper;
        private readonly IIdentityService _identityService;
        private readonly DapperContext _dapperContext;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBranchRepository _branchRepository;
        private readonly IEmployeeRepository _employeeRepository;

        public BranchService(
            IMapper mapper,
            IIdentityService identityService,
            DapperContext dapperContext,
            IUnitOfWork unitOfWork,
            IBranchRepository branchRepository,
            IEmployeeRepository employeeRepository
        )
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _branchRepository = branchRepository ?? throw new ArgumentNullException(nameof(branchRepository));
            _employeeRepository = employeeRepository ?? throw new ArgumentNullException(nameof(employeeRepository));
        }

        public async Task<PageResponseViewModel<List<BranchViewModel>>> GetBranchPage(PageFilterViewModel pageFilter)
        {
            var userId = _identityService.GetUserId();
            using var conn = _dapperContext.CreateConnection();

            var baseSql = @"
                SELECT
                    b.Id,
                    b.Name,
                    b.Code,
                    b.Active,
                    COUNT(e.Id) AS TotalEmployee
                FROM Branchs b WITH (NOLOCK)
                LEFT JOIN Employees e on e.BranchId = b.Id
            ";
            var sqlFilter = "WHERE b.UserId = @UserId";

            if (!string.IsNullOrWhiteSpace(pageFilter.SearchTerm))
            {
                pageFilter.SearchTerm += "%";
                sqlFilter = sqlFilter + " AND " + "(b.Code LIKE @SearchTerm OR b.Name LIKE @SearchTerm)";
            }

            var sqlCount = @"SELECT COUNT(1) FROM Branchs b WITH (NOLOCK) " + sqlFilter;
            var total = await conn.ExecuteScalarAsync<int>(
                sqlCount,
                new
                {
                    UserId = userId,
                    pageFilter.SearchTerm,
                }
            );

            sqlFilter += @"
                GROUP BY
                    b.Id,
                    b.Name,
                    b.Code,
                    b.Active
            ";
            sqlFilter += pageFilter.GetSortQuery("ORDER BY b.Code");
            var sql = baseSql + sqlFilter + " OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY";
            var pageData = (await conn.QueryAsync<BranchViewModel>(
                sql,
                new
                {
                    UserId = userId,
                    pageFilter.SearchTerm,
                    Offset = pageFilter.PageIndex * pageFilter.PageSize,
                    Limit = pageFilter.PageSize
                })
            )
            .ToList();

            var result = PageHelper.CreatePagedReponse(
                pageData,
                pageFilter,
                total
            );

            return result;
        }

        public async Task CreateBranch(BranchDetailViewModel branchViewModel)
        {
            var userId = _identityService.GetUserId();
            var branch = _mapper.Map<Branch>(branchViewModel);
            branch.UserId = userId;
            _branchRepository.Add(branch);

            await _unitOfWork.CommitAsync();
        }

        public async Task UpdateBranch(int id, UpdateBranchViewModel updateBranchViewModel)
        {
            var branch = _branchRepository.AllIncluding(e => e.Address).FirstOrDefault(e => e.Id == id);
            if (branch is null) throw new InvalidOperationException("Branch not found.");

            _mapper.Map(updateBranchViewModel, branch);
            _branchRepository.Update(branch);
            await _unitOfWork.CommitAsync();
        }

        public async Task DeleteBranch(int id, bool deleteEmployee)
        {
            var branch = _branchRepository.GetSingle(id) ?? throw new InvalidOperationException("Branch not found.");

            var employees = _employeeRepository.GetAll().Where(e => e.BranchId == id).ToList();
            if (deleteEmployee)
                _employeeRepository.DeleteRange(employees);
            else
            {
                foreach (var employee in employees)
                {
                    employee.BranchId = null;
                    _employeeRepository.Update(employee);
                }
            }

            _branchRepository.Delete(branch);
            await _unitOfWork.CommitAsync();
        }

        public async Task<BranchDetailViewModel> GetBranch(int id)
        {
            var result = await _branchRepository.GetBranch(id);
            return result;
        }

        public async Task<IEnumerable<BranchViewModel>> GetBranches()
        {
            var userId = _identityService.GetUserId();
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT
                    b.Id,
                    b.Name,
                    b.Code,
                    b.Active,
                    COUNT(e.Id) AS TotalEmployee
                FROM Branchs b WITH (NOLOCK)
                LEFT JOIN Employees e on e.BranchId = b.Id
                WHERE b.UserId = @UserId
                GROUP BY b.Id, b.Name, b.Code, b.Active
                ORDER BY b.Name
            ";

            var result = (await conn.QueryAsync<BranchViewModel>(sql, new { UserId = userId })).ToList();
            return result;
        }

        public async Task<Dictionary<string, int>> GetBranchesCodes(int userId)
        {
            using var conn = _dapperContext.CreateConnection();
            var sql = @"
                SELECT Code,Name
                FROM Branchs
                WHERE UserId = @UserId AND Active = 1
            ";

            var result = (await conn.QueryAsync<BranchViewModel>(sql, new { UserId = userId })).ToDictionary(x => x.Code, x => x.Id);
            return result;
        }

        public async Task UpdateActiveBranch(int id, UpdateBranchActiveViewModel updateBranchViewModel)
        {
            var branch = _branchRepository.GetAll().FirstOrDefault(e => e.Id == id) ?? throw new InvalidOperationException("Branch not found.");
            branch.Active = updateBranchViewModel.Active;
            _branchRepository.Update(branch);
            await _unitOfWork.CommitAsync();
        }
    }
}
