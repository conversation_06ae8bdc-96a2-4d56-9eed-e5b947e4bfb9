﻿using Dapper;
using payroll_api.data;
using payroll_api.utility.ViewModel;
using payroll_api.utility.ViewModel.Reports;

namespace payroll_api.service
{
    public interface IReportService
    {
        Task<EmployeeSlipReportViewModel> GetEmployeeSlipReport(string employeeId, string payrollId);
    }
    public class ReportService : IReportService
    {
        private readonly DapperContext _dapperContext;

        public ReportService(DapperContext dapperContext)
        {
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
        }

        public async Task<EmployeeSlipReportViewModel> GetEmployeeSlipReport(string employeeId, string payrollId)
        {
            using var connection = _dapperContext.CreateConnection();

            var query = @"select p.Code, p.PayDate,p.PayrollDate,
	                        pe.Salary,pe.SocialSecurityFund,pe.Tax,pe.TotalIncome,pe.TotalOutcome,pe.Total,pe.Id as PayrollEmployeeId,pe.Bank,pe.BankAccount,ISNULL(pe.PositionName,'-') as PositionName,
	                        e.FirstName, e.LastName, e.Code as EmployeeCode, e.Email AS EmployeeEmail
                        from Payrolls p with(nolock)
                        inner join PayrollEmployees pe with(nolock) on p.Id = pe.PayrollId
                        inner join Employees e with(nolock) on pe.EmployeeId = e.id
                        where p.Id = @payrollId and pe.EmployeeId = @employeeId";
            var employeeSlipReportViewModel = await connection.QueryFirstOrDefaultAsync<EmployeeSlipReportViewModel>(query, new { employeeId, payrollId });

            query = @"
                -- result 1
                select Amount,Name from PayrollEmployeeIncomes where PayrollEmployeeId = @PayrollEmployeeId AND Amount > 0;
                -- result 2
                select Amount,Name from PayrollEmployeeOutcomes where PayrollEmployeeId = @PayrollEmployeeId AND Amount > 0;
                -- result 3
                select sum(pe.TotalIncome) as TotalIncome,
                    sum(pe.Tax) as TotalTax,
                    sum(pe.SocialSecurityFund) as TotalSocialSecurityFund
                from PayrollEmployees pe with(nolock)
                inner join Payrolls p with(nolock) on p.id = pe.PayrollId
                where EmployeeId = @employeeId
                    and p.Status = 3
                    and p.PayDate is not null 
                    and year(p.PayDate) = @year;
            ";

            using var multi = await connection.QueryMultipleAsync(query, new { employeeSlipReportViewModel.PayrollEmployeeId, employeeId, year = employeeSlipReportViewModel.PayDate?.Year ?? null });
            employeeSlipReportViewModel.PayrollEmployeeIncomes = (await multi.ReadAsync<PayrollEmployeeIncomeViewModel>()).ToList();
            employeeSlipReportViewModel.PayrollEmployeeOutcomes = (await multi.ReadAsync<PayrollEmployeeOutcomeViewModel>()).ToList();
            employeeSlipReportViewModel.Summary = await multi.ReadFirstAsync<EmployeeSlipReportSummaryViewModel>();

            return employeeSlipReportViewModel;
        }
    }
}
