﻿using AutoMapper;
using payroll_api.domain;
using payroll_api.domain.Payrolls;
using payroll_api.domain.Settings;
using payroll_api.utility.ViewModel;

namespace payroll_api.service.Infrastructure.Mapping
{
    public class ViewModelToDomainMappingProfile : Profile
    {
        public ViewModelToDomainMappingProfile()
        {
            CreateMap<CreateEmployeeViewModel, Employee>();
            CreateMap<UpdateEmployeeViewModel, Employee>();
            CreateMap<CreatePayrollViewModel, Payroll>();
            CreateMap<PayrollEmployeeViewModel, PayrollEmployee>();
            CreateMap<PayrollEmployeeIncomeViewModel, PayrollEmployeeIncome>();
            CreateMap<PayrollEmployeeOutcomeViewModel, PayrollEmployeeOutcome>();
            CreateMap<SettingImageViewModel, SettingImage>();
            CreateMap<RegisterUserViewModel, User>();
            CreateMap<UserProfileViewModel, User>();
            CreateMap<UpdateUserViewModel, User>();
            CreateMap<BranchDetailViewModel, Branch>();
            CreateMap<AddressViewModel, BranchAddress>();
        }
    }
}
