﻿using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.service.Infrastructure.Helper
{
    public static class PageHelper
    {
        public static PageResponseViewModel<List<T>> CreatePagedReponse<T>(List<T> pagedData, PageFilterViewModel validFilter, int totalRecords)
        {
            var respose = new PageResponseViewModel<List<T>>(pagedData, validFilter.PageIndex, validFilter.PageSize);
            var totalPages = (double)totalRecords / validFilter.PageSize;
            int roundedTotalPages = Convert.ToInt32(Math.Ceiling(totalPages));
            respose.TotalPages = roundedTotalPages;
            respose.TotalRecords = totalRecords;
            return respose;
        }
    }
}
