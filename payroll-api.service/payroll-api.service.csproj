<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>payroll_api.service</RootNamespace>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="ClosedXML" Version="0.102.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\payroll-api.data\payroll-api.data.csproj" />
    <ProjectReference Include="..\payroll-api.email\payroll-api.email.csproj" />
    <ProjectReference Include="..\payroll-api.report\payroll-api.report.csproj" />
    <ProjectReference Include="..\payroll-api.utility\payroll-api.utility.csproj" />
  </ItemGroup>

</Project>
