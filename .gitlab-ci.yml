image: mcr.microsoft.com/dotnet/sdk:8.0

include:
  - template: Jobs/Dependency-Scanning.gitlab-ci.yml
  - template: Jobs/SAST.gitlab-ci.yml
  - template: Jobs/Secret-Detection.gitlab-ci.yml

stages:
  - build
  - test
  - security
  - package
  - deploy

variables:
  APP_VERSION: $CI_COMMIT_SHA
  BUILD_SCRIPT: "infra/build-app.sh"
  TEST_SCRIPT: "infra/test-app.sh"
  DOCKER_SETUP_SCRIPT: "infra/setup-docker.sh"
  DOCKER_BUILD_SCRIPT: "infra/build-image.sh"
  DOCKER_PUSH_SCRIPT: "infra/push-image.sh"
  SSH_SETUP_SCRIPT: "infra/setup-ssh.sh"
  DEPLOY_SCRIPT: "infra/deploy.sh"

build:
  stage: build
  script:
    - chmod +x ${BUILD_SCRIPT} && ${BUILD_SCRIPT}

test:
  stage: test
  script:
    - chmod +x ${TEST_SCRIPT} && ${TEST_SCRIPT}

build docker:
  stage: package
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    - chmod +x ${DOCKER_SETUP_SCRIPT} && ${DOCKER_SETUP_SCRIPT}
  script:
    - chmod +x ${DOCKER_BUILD_SCRIPT} && ${DOCKER_BUILD_SCRIPT}
    - chmod +x ${DOCKER_PUSH_SCRIPT} && ${DOCKER_PUSH_SCRIPT}
  only:
    - tags

sast:
  stage: security
  artifacts:
    paths:
      - gl-sast-report.json
    expire_in: 1 days

deploy:
  stage: deploy
  image:
    name: amazon/aws-cli:2.4.11
    entrypoint: [""]
  variables:
    APP_NAME: Payroll API
    APP_ENV_NAME: PayrollAPI-env
  script:
    - yum install -y gettext
    - export DEPLOY_TOKEN=$(echo $GITLAB_DEPLOY_TOKEN | tr -d "\n" | base64)
    - envsubst < templates/Dockerrun.aws.json > Dockerrun.aws.json
    - envsubst < templates/auth.json > auth.json
    - cat Dockerrun.aws.json
    - cat auth.json
    - aws s3 cp Dockerrun.aws.json s3://$AWS_S3_BUCKET/Dockerrun.aws.json
    - aws s3 cp auth.json s3://$AWS_S3_BUCKET/auth.json
    - aws elasticbeanstalk create-application-version --application-name "$APP_NAME" --version-label $APP_VERSION --source-bundle S3Bucket=$AWS_S3_BUCKET,S3Key=Dockerrun.aws.json
    - aws elasticbeanstalk update-environment --application-name "$APP_NAME" --version-label $APP_VERSION --environment-name $APP_ENV_NAME
  when: manual

gemnasium-dependency_scanning:
  # tags: [ saas-linux-large-amd64 ]
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_MERGE_REQUEST_IID