- EF Core .NET Command-line Tools(https://docs.microsoft.com/en-us/ef/core/miscellaneous/cli/dotnet)

# Code first:
dotnet ef migrations add AddIsEmailVerifiedToUser -o Migrations --project payroll-api.data --startup-project payroll-api --context PayrollContext
dotnet ef database update --project payroll-api.data --startup-project payroll-api --context PayrollContext
dotnet ef migrations remove --project .\payroll-api.data --startup-project payroll-api --context PayrollContext

# MACOS
dotnet ef migrations add AddIsEmailVerifiedToUser -o Migrations --project ../payroll-api.data --startup-project ../payroll-api --context PayrollContext
dotnet ef database update --project ../payroll-api.data --startup-project ../payroll-api --context PayrollContext
dotnet ef migrations remove --project ../payroll-api.data --startup-project ../payroll-api --context PayrollContext