﻿using Microsoft.EntityFrameworkCore;
using payroll_api.data.Configuration;
using payroll_api.domain;
using payroll_api.domain.Payrolls;
using payroll_api.domain.Settings;

namespace payroll_api.data
{
    public class PayrollContext : DbContext
    {
        public PayrollContext()
        {
        }

        public PayrollContext(DbContextOptions<PayrollContext> options) : base(options)
        {
        }
        

        public DbSet<Department> Departments { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Position> Positions { get; set; }
        public DbSet<Setting> Settings { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Income> Incomes { get; set; }
        public DbSet<Outcome> Outcomes { get; set; }
        public DbSet<EmployeeAddress> EmployeeAddresses { get; set; }
        public DbSet<Payroll> Payrolls { get; set; }
        public DbSet<PayrollEmployee> PayrollEmployees { get; set; }
        public DbSet<PayrollHistory> PayrollHistories { get; set; }
        public DbSet<PayrollEmployeeIncome> PayrollEmployeeIncomes { get; set; }
        public DbSet<PayrollEmployeeOutcome> PayrollEmployeeOutcomes { get; set; }
        public DbSet<SettingImage> SettingImages { get; set; }
        public DbSet<Branch> Branchs { get; set; }
        public DbSet<BranchAddress> BranchAddresses { get; set; }
        public DbSet<AutoEmail> AutoEmails { get; set; }
        public DbSet<EmailVerificationToken> EmailVerificationTokens { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new DepartmentConfiguration());
            modelBuilder.ApplyConfiguration(new EmployeeConfiguration());
            modelBuilder.ApplyConfiguration(new PositionConfiguration());
            modelBuilder.ApplyConfiguration(new SettingConfiguration());
            modelBuilder.ApplyConfiguration(new UserConfiguration());
            modelBuilder.ApplyConfiguration(new RefreshTokenConfiguration());
            modelBuilder.ApplyConfiguration(new IncomeConfiguration());
            modelBuilder.ApplyConfiguration(new OutcomeConfiguration());
            modelBuilder.ApplyConfiguration(new EmployeeAddressConfiguration());
            modelBuilder.ApplyConfiguration(new PayrollConfiguration());
            modelBuilder.ApplyConfiguration(new PayrollEmployeeConfiguration());
            modelBuilder.ApplyConfiguration(new PayrollHistoryConfiguration());
            modelBuilder.ApplyConfiguration(new PayrollEmployeeIncomeConfiguration());
            modelBuilder.ApplyConfiguration(new PayrollEmployeeOutcomeConfiguration());
            modelBuilder.ApplyConfiguration(new SettingImageConfiguration());
            modelBuilder.ApplyConfiguration(new BranchConfiguration());
            modelBuilder.ApplyConfiguration(new BranchAddressConfiguration());
            modelBuilder.ApplyConfiguration(new AutoEmailConfiguration());
            modelBuilder.ApplyConfiguration(new EmailVerificationTokenConfiguration());
        }
    }
}
