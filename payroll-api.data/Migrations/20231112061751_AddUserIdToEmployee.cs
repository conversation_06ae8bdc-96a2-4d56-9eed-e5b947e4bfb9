﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddUserIdToEmployee : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "UserId",
                table: "Employees",
                type: "int",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 12, 13, 17, 50, 868, DateTimeKind.Local).AddTicks(3721), "$2a$11$/dfh8rm/JV/d0TIBhpcg.uEP/OMUFLUAwx.xcrOOFqOB6MB9C6GJ.", new DateTime(2023, 11, 12, 13, 17, 50, 868, DateTimeKind.Local).AddTicks(3701) });

            migrationBuilder.CreateIndex(
                name: "IX_Employees_UserId",
                table: "Employees",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Employees_Users_UserId",
                table: "Employees",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Employees_Users_UserId",
                table: "Employees");

            migrationBuilder.DropIndex(
                name: "IX_Employees_UserId",
                table: "Employees");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "Employees");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 12, 12, 56, 51, 684, DateTimeKind.Local).AddTicks(2368), "$2a$11$T4frlYsfVrH5J8Lp2U/h7OzidDWUoF91Lel7dy73DFjM7om2s3gSG", new DateTime(2023, 11, 12, 12, 56, 51, 684, DateTimeKind.Local).AddTicks(2349) });
        }
    }
}
