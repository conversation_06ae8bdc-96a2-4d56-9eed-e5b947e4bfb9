﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddAvtiveBranchEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Active",
                table: "Branchs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                table: "Branchs",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 9, 23, 0, 22, 45, 776, DateTimeKind.Local).AddTicks(7254), "$2a$11$umQ.V.Z0llnfsvDIjyV3nesa.e1Zqb.AIw1BHZK4ixG1QGs9m6rnW", new DateTime(2024, 9, 23, 0, 22, 45, 776, DateTimeKind.Local).AddTicks(7234) });

            migrationBuilder.CreateIndex(
                name: "IX_Branchs_UserId",
                table: "Branchs",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Branchs_Users_UserId",
                table: "Branchs",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Branchs_Users_UserId",
                table: "Branchs");

            migrationBuilder.DropIndex(
                name: "IX_Branchs_UserId",
                table: "Branchs");

            migrationBuilder.DropColumn(
                name: "Active",
                table: "Branchs");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "Branchs");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 9, 22, 15, 26, 29, 490, DateTimeKind.Local).AddTicks(2330), "$2a$11$1eMe2P3bB3RQqopkONbO6uzOcCk2kQnOwD7Gnw3uKNmlgqeqZIBcW", new DateTime(2024, 9, 22, 15, 26, 29, 490, DateTimeKind.Local).AddTicks(2290) });
        }
    }
}
