﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class DropUserTutorialSteps : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_UserTutorialSteps",
                table: "UserTutorialSteps");

            migrationBuilder.AddColumn<int>(
                name: "UserTutorialStepId",
                table: "UserTutorialSteps",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UserTutorialSteps",
                table: "UserTutorialSteps",
                columns: new[] { "UserTutorialStepId", "UserId", "TutorialStepId" });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 3, 24, 20, 42, 21, 83, DateTimeKind.Local).AddTicks(5035), "$2a$11$ezedCGHmvF321g4bTK/6suzEExjIybJS3cVT9rhS35WjJoPic4qEu", new DateTime(2024, 3, 24, 20, 42, 21, 83, DateTimeKind.Local).AddTicks(5011) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_UserTutorialSteps",
                table: "UserTutorialSteps");

            migrationBuilder.DropColumn(
                name: "UserTutorialStepId",
                table: "UserTutorialSteps");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UserTutorialSteps",
                table: "UserTutorialSteps",
                columns: new[] { "Id", "UserId", "TutorialStepId" });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 3, 24, 14, 20, 20, 888, DateTimeKind.Local).AddTicks(8298), "$2a$11$HJD.9TyxmplT6H5/bBofQOk7zlBeIUFbNqys8sRrUi3iOV5Fs0ASO", new DateTime(2024, 3, 24, 14, 20, 20, 888, DateTimeKind.Local).AddTicks(8288) });
        }
    }
}
