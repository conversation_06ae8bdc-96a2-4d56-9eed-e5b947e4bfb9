﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class SetIdUserTutorialStepToPK : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_UserTutorialSteps",
                table: "UserTutorialSteps");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UserTutorialSteps",
                table: "UserTutorialSteps",
                columns: new[] { "Id", "UserId", "TutorialStepId" });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 3, 24, 14, 20, 20, 888, DateTimeKind.Local).AddTicks(8298), "$2a$11$HJD.9TyxmplT6H5/bBofQOk7zlBeIUFbNqys8sRrUi3iOV5Fs0ASO", new DateTime(2024, 3, 24, 14, 20, 20, 888, DateTimeKind.Local).AddTicks(8288) });

            migrationBuilder.CreateIndex(
                name: "IX_UserTutorialSteps_UserId",
                table: "UserTutorialSteps",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_UserTutorialSteps",
                table: "UserTutorialSteps");

            migrationBuilder.DropIndex(
                name: "IX_UserTutorialSteps_UserId",
                table: "UserTutorialSteps");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UserTutorialSteps",
                table: "UserTutorialSteps",
                columns: new[] { "UserId", "TutorialStepId" });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 3, 24, 14, 6, 38, 294, DateTimeKind.Local).AddTicks(1012), "$2a$11$wubgIPrceJnbwdto3hX93enUVAPRE6.uq0xita5ahiX2hf/J5OSLu", new DateTime(2024, 3, 24, 14, 6, 38, 294, DateTimeKind.Local).AddTicks(1001) });
        }
    }
}
