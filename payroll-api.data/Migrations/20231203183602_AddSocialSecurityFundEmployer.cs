﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddSocialSecurityFundEmployer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "SocialSecurityFundEmployer",
                table: "Payrolls",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 4, 1, 36, 1, 933, DateTimeKind.Local).AddTicks(3259), "$2a$11$IU.zHugJa2mK011OPFp9JeSjv/MbWgANUqXTL5QCY/zqQv4NtiWwi", new DateTime(2023, 12, 4, 1, 36, 1, 933, DateTimeKind.Local).AddTicks(3239) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SocialSecurityFundEmployer",
                table: "Payrolls");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 3, 1, 58, 59, 708, DateTimeKind.Local).AddTicks(7167), "$2a$11$mzyNyDV4AKJA/qEHMmiZwu2wDjQWK5VqsP8AnTDdgCQI6E6hph7Pe", new DateTime(2023, 12, 3, 1, 58, 59, 708, DateTimeKind.Local).AddTicks(7149) });
        }
    }
}
