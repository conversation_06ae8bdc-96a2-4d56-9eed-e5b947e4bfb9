﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class RecreateIdFromUserTutorialSteps : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "UserTutorialStepId",
                table: "UserTutorialSteps",
                newName: "Id");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 3, 24, 20, 53, 40, 735, DateTimeKind.Local).AddTicks(2405), "$2a$11$L/DG.EPekCtCQALfnAuRQOrvGgVWbtEfUdaYgOpnuF98BBmUwpfkG", new DateTime(2024, 3, 24, 20, 53, 40, 735, DateTimeKind.Local).AddTicks(2388) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Id",
                table: "UserTutorialSteps",
                newName: "UserTutorialStepId");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 3, 24, 20, 51, 39, 129, DateTimeKind.Local).AddTicks(4481), "$2a$11$.MDiPEOU9AiaCQtlk0TsUOkzWOUnmrypjd/p7fS90KHXpaLrjzacq", new DateTime(2024, 3, 24, 20, 51, 39, 129, DateTimeKind.Local).AddTicks(4454) });
        }
    }
}
