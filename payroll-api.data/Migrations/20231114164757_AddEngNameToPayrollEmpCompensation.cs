﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddEngNameToPayrollEmpCompensation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Type",
                table: "PayrollEmployeeCompensation");

            migrationBuilder.AddColumn<string>(
                name: "EngName",
                table: "PayrollEmployeeCompensation",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 14, 23, 47, 56, 499, DateTimeKind.Local).AddTicks(5850), "$2a$11$GQgnOGq4hKGkkl0E5kiKqO4dCcHF6aomS.R9hLx7JzeR2lnI9dVRe", new DateTime(2023, 11, 14, 23, 47, 56, 499, DateTimeKind.Local).AddTicks(5829) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EngName",
                table: "PayrollEmployeeCompensation");

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "PayrollEmployeeCompensation",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 14, 21, 22, 28, 755, DateTimeKind.Local).AddTicks(2796), "$2a$11$7mHoC9Xa5J9rNwKte.Lc1.fBAsbJlIp9TL06u9T1nsYg3fVw6mO.K", new DateTime(2023, 11, 14, 21, 22, 28, 755, DateTimeKind.Local).AddTicks(2770) });
        }
    }
}
