﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddEmployeeAddressModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EmployeeAddress",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false),
                    Address = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    SubDistrict = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    District = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Province = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ZipCode = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmployeeAddress", x => x.ID);
                    table.ForeignKey(
                        name: "FK_EmployeeAddress_Employees_ID",
                        column: x => x.ID,
                        principalTable: "Employees",
                        principalColumn: "ID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 13, 16, 36, 27, 286, DateTimeKind.Local).AddTicks(5998), "$2a$11$9ZErZuV06MUG0mSsLXthIO4yw.Tbm3ferafX4QtjhiXG5O/9Ufy8i", new DateTime(2023, 8, 13, 16, 36, 27, 286, DateTimeKind.Local).AddTicks(5988) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EmployeeAddress");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 13, 16, 19, 42, 691, DateTimeKind.Local).AddTicks(5220), "$2a$11$WXyPwCYB3U67bPtzjlM7Ze1WeKW.ropwjKMXVInL5crgatUuwe9wS", new DateTime(2023, 8, 13, 16, 19, 42, 691, DateTimeKind.Local).AddTicks(5209) });
        }
    }
}
