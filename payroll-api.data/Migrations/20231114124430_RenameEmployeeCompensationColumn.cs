﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class RenameEmployeeCompensationColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Value",
                table: "PayrollEmployeeCompensation",
                newName: "Amount");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 14, 19, 44, 29, 658, DateTimeKind.Local).AddTicks(8061), "$2a$11$WpHceIDezXvU9.jR37gs5uxf4EOfhm6IMftLLrIe/M1nJad0cNuE6", new DateTime(2023, 11, 14, 19, 44, 29, 658, DateTimeKind.Local).AddTicks(8042) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Amount",
                table: "PayrollEmployeeCompensation",
                newName: "Value");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 12, 16, 24, 2, 833, DateTimeKind.Local).AddTicks(1294), "$2a$11$4YYPuavH6TVn9E85D6GOuumM8VcjNJ0frzvmsEkpYsZxCKzCEqxmu", new DateTime(2023, 11, 12, 16, 24, 2, 833, DateTimeKind.Local).AddTicks(1284) });
        }
    }
}
