﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddEmployeeTypeAndPosition : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "EmployeeType",
                table: "Employees",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "PositionID",
                table: "Employees",
                type: "int",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 14, 14, 42, 9, 876, DateTimeKind.Local).AddTicks(5275), "$2a$11$.atdeDbUI.GOVDj5bZFGGe2Iz0QhpoQXfKRu8msUo73gnuf39BtxS", new DateTime(2023, 8, 14, 14, 42, 9, 876, DateTimeKind.Local).AddTicks(5263) });

            migrationBuilder.CreateIndex(
                name: "IX_Employees_PositionID",
                table: "Employees",
                column: "PositionID");

            migrationBuilder.AddForeignKey(
                name: "FK_Employees_Positions_PositionID",
                table: "Employees",
                column: "PositionID",
                principalTable: "Positions",
                principalColumn: "ID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Employees_Positions_PositionID",
                table: "Employees");

            migrationBuilder.DropIndex(
                name: "IX_Employees_PositionID",
                table: "Employees");

            migrationBuilder.DropColumn(
                name: "EmployeeType",
                table: "Employees");

            migrationBuilder.DropColumn(
                name: "PositionID",
                table: "Employees");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 14, 13, 13, 29, 711, DateTimeKind.Local).AddTicks(429), "$2a$11$z81jB7MnUbv9pUwdhSQxseeGOSBWw58G3ZEM0zoryycTJrS4TxvsS", new DateTime(2023, 8, 14, 13, 13, 29, 711, DateTimeKind.Local).AddTicks(417) });
        }
    }
}
