﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class NullablePayDatePayroll : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "PayDate",
                table: "Payrolls",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 3, 1, 42, 52, 10, DateTimeKind.Local).AddTicks(8134), "$2a$11$xV4GL07y/8cZxfc66Yxd/O0Wn5qH3/PVsuCr7xud0Z6FgAlPVGIx2", new DateTime(2023, 12, 3, 1, 42, 52, 10, DateTimeKind.Local).AddTicks(8119) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "PayDate",
                table: "Payrolls",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 3, 0, 53, 45, 678, DateTimeKind.Local).AddTicks(485), "$2a$11$FV/eyx4wSu0rNfLirN.fsuHFw1xk8xhgdxVHrpus3nIygziUD.tne", new DateTime(2023, 12, 3, 0, 53, 45, 678, DateTimeKind.Local).AddTicks(469) });
        }
    }
}
