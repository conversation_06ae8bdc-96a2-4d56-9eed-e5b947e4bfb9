﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class RemoveUserTutorial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserTutorialSteps");

            migrationBuilder.DropTable(
                name: "TutorialSteps");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 8, 11, 18, 32, 35, 825, DateTimeKind.Local).AddTicks(1450), "$2a$11$grUkD4W2h7U/ZHHoiLwa4ejx2yNtP3NMfBywsgloIwPQGI6lRnj/C", new DateTime(2024, 8, 11, 18, 32, 35, 825, DateTimeKind.Local).AddTicks(1390) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "TutorialSteps",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TutorialSteps", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UserTutorialSteps",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    TutorialStepId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserTutorialSteps", x => new { x.Id, x.UserId, x.TutorialStepId });
                    table.ForeignKey(
                        name: "FK_UserTutorialSteps_TutorialSteps_TutorialStepId",
                        column: x => x.TutorialStepId,
                        principalTable: "TutorialSteps",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserTutorialSteps_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3023, 7, 31, 1, 31, 38, 133, DateTimeKind.Local).AddTicks(9940), "$2a$11$6Jg9.ok6ognF2hrx4U1AculVVidl7dD6HSPl1uatSAYI4qcL2./zO", new DateTime(2024, 7, 31, 1, 31, 38, 133, DateTimeKind.Local).AddTicks(9830) });

            migrationBuilder.CreateIndex(
                name: "IX_UserTutorialSteps_TutorialStepId",
                table: "UserTutorialSteps",
                column: "TutorialStepId");

            migrationBuilder.CreateIndex(
                name: "IX_UserTutorialSteps_UserId",
                table: "UserTutorialSteps",
                column: "UserId");
        }
    }
}
