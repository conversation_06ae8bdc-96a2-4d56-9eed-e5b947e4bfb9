﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class FixPayrollEmployeeFK : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PayrollEmployees_Employees_Id",
                table: "PayrollEmployees");

            migrationBuilder.DropForeignKey(
                name: "FK_PayrollEmployees_Payrolls_Id",
                table: "PayrollEmployees");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PayrollEmployees",
                table: "PayrollEmployees");

            migrationBuilder.AddColumn<int>(
                name: "PayrollEmployeeId",
                table: "PayrollEmployees",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PayrollEmployees",
                table: "PayrollEmployees",
                column: "PayrollEmployeeId");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 16, 22, 5, 28, 502, DateTimeKind.Local).AddTicks(3563), "$2a$11$oNkP.jaYvOyNUaXKu11kd.fhS53L4nmE69FLrWR6UmayJrcuonapG", new DateTime(2023, 9, 16, 22, 5, 28, 502, DateTimeKind.Local).AddTicks(3538) });

            migrationBuilder.CreateIndex(
                name: "IX_PayrollEmployees_EmployeeId",
                table: "PayrollEmployees",
                column: "EmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollEmployees_PayrollId",
                table: "PayrollEmployees",
                column: "PayrollId");

            migrationBuilder.AddForeignKey(
                name: "FK_PayrollEmployees_Employees_EmployeeId",
                table: "PayrollEmployees",
                column: "EmployeeId",
                principalTable: "Employees",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PayrollEmployees_Payrolls_PayrollId",
                table: "PayrollEmployees",
                column: "PayrollId",
                principalTable: "Payrolls",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PayrollEmployees_Employees_EmployeeId",
                table: "PayrollEmployees");

            migrationBuilder.DropForeignKey(
                name: "FK_PayrollEmployees_Payrolls_PayrollId",
                table: "PayrollEmployees");

            migrationBuilder.DropPrimaryKey(
                name: "PK_PayrollEmployees",
                table: "PayrollEmployees");

            migrationBuilder.DropIndex(
                name: "IX_PayrollEmployees_EmployeeId",
                table: "PayrollEmployees");

            migrationBuilder.DropIndex(
                name: "IX_PayrollEmployees_PayrollId",
                table: "PayrollEmployees");

            migrationBuilder.DropColumn(
                name: "PayrollEmployeeId",
                table: "PayrollEmployees");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PayrollEmployees",
                table: "PayrollEmployees",
                column: "Id");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 16, 16, 54, 41, 453, DateTimeKind.Local).AddTicks(8249), "$2a$11$den/wp4RHBK58b2jo1bWE.QVkdyUFphFSjvo8i8THhmEoFa.7WLhq", new DateTime(2023, 9, 16, 16, 54, 41, 453, DateTimeKind.Local).AddTicks(8231) });

            migrationBuilder.AddForeignKey(
                name: "FK_PayrollEmployees_Employees_Id",
                table: "PayrollEmployees",
                column: "Id",
                principalTable: "Employees",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_PayrollEmployees_Payrolls_Id",
                table: "PayrollEmployees",
                column: "Id",
                principalTable: "Payrolls",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
