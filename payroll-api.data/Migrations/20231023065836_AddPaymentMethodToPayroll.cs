﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddPaymentMethodToPayroll : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PayMethod",
                table: "Payrolls",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 23, 13, 58, 35, 627, DateTimeKind.Local).AddTicks(4608), "$2a$11$9jG/20L2eTstexXMgiYls.zRf5DdjbKvUmMgw9EKVuzKJkQ2K9b9K", new DateTime(2023, 10, 23, 13, 58, 35, 627, DateTimeKind.Local).AddTicks(4594) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PayMethod",
                table: "Payrolls");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 1, 16, 44, 43, 479, DateTimeKind.Local).AddTicks(9170), "$2a$11$eF18SEpLn5zqdB7uV9SWoemupWuSBNkxAm7m2DDmQCB9kmpikn0c2", new DateTime(2023, 10, 1, 16, 44, 43, 479, DateTimeKind.Local).AddTicks(9156) });
        }
    }
}
