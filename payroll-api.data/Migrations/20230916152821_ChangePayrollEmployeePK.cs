﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class ChangePayrollEmployeePK : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PayrollEmployeeId",
                table: "PayrollEmployees",
                newName: "Id");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 16, 22, 28, 21, 101, DateTimeKind.Local).AddTicks(1834), "$2a$11$RFoDe8GXKmbeGhHMT4CGful5K9Fx2lVOYpsMyGi9tkYu4HNYxIYL2", new DateTime(2023, 9, 16, 22, 28, 21, 101, DateTimeKind.Local).AddTicks(1810) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Id",
                table: "PayrollEmployees",
                newName: "PayrollEmployeeId");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 16, 22, 24, 42, 672, DateTimeKind.Local).AddTicks(2079), "$2a$11$votbTcCCqC4QCnluXjccS.5aKcXeKghmcWo/XC3gVVKA0YCRF4qQW", new DateTime(2023, 9, 16, 22, 24, 42, 672, DateTimeKind.Local).AddTicks(2054) });
        }
    }
}
