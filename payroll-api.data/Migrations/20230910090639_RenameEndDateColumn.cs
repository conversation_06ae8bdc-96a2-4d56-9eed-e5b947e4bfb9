﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class RenameEndDateColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ToDate",
                table: "Payrolls",
                newName: "EndDate");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 10, 16, 6, 38, 936, DateTimeKind.Local).AddTicks(8801), "$2a$11$5uuQ6Mhw7ngUUdz9YU88I.2EkPzWImLIGsRATpdJh8NK2bjvnnNYW", new DateTime(2023, 9, 10, 16, 6, 38, 936, DateTimeKind.Local).AddTicks(8787) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "EndDate",
                table: "Payrolls",
                newName: "ToDate");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 10, 15, 47, 35, 389, DateTimeKind.Local).AddTicks(3180), "$2a$11$6SUzkqkwCb8/RRBhYBv.7ev21cTlZbLc6cXWvbcTeI2/chVGXE5fC", new DateTime(2023, 9, 10, 15, 47, 35, 389, DateTimeKind.Local).AddTicks(3163) });
        }
    }
}
