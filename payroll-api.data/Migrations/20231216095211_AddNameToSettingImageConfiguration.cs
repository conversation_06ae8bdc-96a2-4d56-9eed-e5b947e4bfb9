﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddNameToSettingImageConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "SettingImages",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 16, 16, 52, 11, 19, DateTimeKind.Local).AddTicks(440), "$2a$11$60RcZHrCH0bm51XTGq8JFeSpOV2Vbs2XTMU/5rVu4CoSzijuQxdUa", new DateTime(2023, 12, 16, 16, 52, 11, 19, DateTimeKind.Local).AddTicks(410) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "SettingImages",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 16, 16, 35, 31, 963, DateTimeKind.Local).AddTicks(2690), "$2a$11$a6O6brIqca0bfpZgCUlWjuw.RWnb3UeY14iVPXypLq7C24IhBcbXu", new DateTime(2023, 12, 16, 16, 35, 31, 963, DateTimeKind.Local).AddTicks(2650) });
        }
    }
}
