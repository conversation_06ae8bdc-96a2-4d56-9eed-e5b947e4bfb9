﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddIsSocialsecurity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsSocialSecurity",
                table: "Employees",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 27, 21, 9, 19, 800, DateTimeKind.Local).AddTicks(7313), "$2a$11$Yws7D2rwONrIckCjtQ.louOsUgDbb4yLs8Q9LeuMp5fQyJOAFjx/.", new DateTime(2023, 11, 27, 21, 9, 19, 800, DateTimeKind.Local).AddTicks(7293) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSocialSecurity",
                table: "Employees");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 26, 23, 48, 24, 420, DateTimeKind.Local).AddTicks(9924), "$2a$11$NWFEnAa3nrV33Krmt9Ebs.O/av27ozg4ujY.ffccGdJTctl4JVDCW", new DateTime(2023, 11, 26, 23, 48, 24, 420, DateTimeKind.Local).AddTicks(9907) });
        }
    }
}
