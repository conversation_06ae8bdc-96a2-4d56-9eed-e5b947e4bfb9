﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class RenameAutoEmailTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AutoEmail_Users_UserId",
                table: "AutoEmail");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AutoEmail",
                table: "AutoEmail");

            migrationBuilder.RenameTable(
                name: "AutoEmail",
                newName: "AutoEmails");

            migrationBuilder.RenameIndex(
                name: "IX_AutoEmail_UserId",
                table: "AutoEmails",
                newName: "IX_AutoEmails_UserId");

            migrationBuilder.AddColumn<int>(
                name: "Id",
                table: "AutoEmails",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddPrimaryKey(
                name: "PK_AutoEmails",
                table: "AutoEmails",
                column: "AutoEmailId");

            migrationBuilder.AddForeignKey(
                name: "FK_AutoEmails_Users_UserId",
                table: "AutoEmails",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AutoEmails_Users_UserId",
                table: "AutoEmails");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AutoEmails",
                table: "AutoEmails");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "AutoEmails");

            migrationBuilder.RenameTable(
                name: "AutoEmails",
                newName: "AutoEmail");

            migrationBuilder.RenameIndex(
                name: "IX_AutoEmails_UserId",
                table: "AutoEmail",
                newName: "IX_AutoEmail_UserId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AutoEmail",
                table: "AutoEmail",
                column: "AutoEmailId");

            migrationBuilder.AddForeignKey(
                name: "FK_AutoEmail_Users_UserId",
                table: "AutoEmail",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
