﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddBankInfoToPayrollEmployee : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Bank",
                table: "PayrollEmployees",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "BankAccount",
                table: "PayrollEmployees",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 3, 1, 54, 4, 48, DateTimeKind.Local).AddTicks(8110), "$2a$11$jO8mpOloJZaZt.JYfUPDgeIc/mj96Drcp6kwsHyPH2oISlSvwJoZO", new DateTime(2023, 12, 3, 1, 54, 4, 48, DateTimeKind.Local).AddTicks(8096) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Bank",
                table: "PayrollEmployees");

            migrationBuilder.DropColumn(
                name: "BankAccount",
                table: "PayrollEmployees");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 3, 1, 42, 52, 10, DateTimeKind.Local).AddTicks(8134), "$2a$11$xV4GL07y/8cZxfc66Yxd/O0Wn5qH3/PVsuCr7xud0Z6FgAlPVGIx2", new DateTime(2023, 12, 3, 1, 42, 52, 10, DateTimeKind.Local).AddTicks(8119) });
        }
    }
}
