﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddPayrollToPayrollHistory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PayrollId",
                table: "PayrollHistories",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 1, 16, 44, 43, 479, DateTimeKind.Local).AddTicks(9170), "$2a$11$eF18SEpLn5zqdB7uV9SWoemupWuSBNkxAm7m2DDmQCB9kmpikn0c2", new DateTime(2023, 10, 1, 16, 44, 43, 479, DateTimeKind.Local).AddTicks(9156) });

            migrationBuilder.CreateIndex(
                name: "IX_PayrollHistories_PayrollId",
                table: "PayrollHistories",
                column: "PayrollId");

            migrationBuilder.AddForeignKey(
                name: "FK_PayrollHistories_Payrolls_PayrollId",
                table: "PayrollHistories",
                column: "PayrollId",
                principalTable: "Payrolls",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PayrollHistories_Payrolls_PayrollId",
                table: "PayrollHistories");

            migrationBuilder.DropIndex(
                name: "IX_PayrollHistories_PayrollId",
                table: "PayrollHistories");

            migrationBuilder.DropColumn(
                name: "PayrollId",
                table: "PayrollHistories");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 1, 15, 30, 7, 408, DateTimeKind.Local).AddTicks(9833), "$2a$11$XjpX4XlfwNkFUD4.daPnveSyZEMbfXQu4CG.4Av2f0z85tzubQuku", new DateTime(2023, 10, 1, 15, 30, 7, 408, DateTimeKind.Local).AddTicks(9823) });
        }
    }
}
