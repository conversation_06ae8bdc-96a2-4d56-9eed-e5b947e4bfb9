﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddCodeColumnToEmployee : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "Employees",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 10, 17, 4, 22, 926, DateTimeKind.Local).AddTicks(2459), "$2a$11$oE0SgygB/xjtbiuHiRBvM.Jh6JLM1qN9AajKxnNrZ61dtp32YAmpm", new DateTime(2023, 9, 10, 17, 4, 22, 926, DateTimeKind.Local).AddTicks(2444) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Code",
                table: "Employees");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 10, 16, 49, 42, 996, DateTimeKind.Local).AddTicks(9168), "$2a$11$olAV5wz6FcoRR6AUQFtL9.1cNWlJica2CG.K2T3GIUb8zqpG3gNba", new DateTime(2023, 9, 10, 16, 49, 42, 996, DateTimeKind.Local).AddTicks(9152) });
        }
    }
}
