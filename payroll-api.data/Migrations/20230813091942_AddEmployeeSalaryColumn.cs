﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddEmployeeSalaryColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Sal<PERSON>",
                table: "Employees",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 13, 16, 19, 42, 691, DateTimeKind.Local).AddTicks(5220), "$2a$11$WXyPwCYB3U67bPtzjlM7Ze1WeKW.ropwjKMXVInL5crgatUuwe9wS", new DateTime(2023, 8, 13, 16, 19, 42, 691, DateTimeKind.Local).AddTicks(5209) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Salary",
                table: "Employees");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 7, 29, 14, 18, 5, 702, DateTimeKind.Local).AddTicks(3854), "$2a$11$2k272BcsnDHvHmgV00jJZuUGKXspy/pqTbedi7.ehk.r7Vt19AsCC", new DateTime(2023, 7, 29, 14, 18, 5, 702, DateTimeKind.Local).AddTicks(3844) });
        }
    }
}
