﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddStartDateEmployeeModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "StartDate",
                table: "Employees",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 14, 13, 13, 29, 711, DateTimeKind.Local).AddTicks(429), "$2a$11$z81jB7MnUbv9pUwdhSQxseeGOSBWw58G3ZEM0zoryycTJrS4TxvsS", new DateTime(2023, 8, 14, 13, 13, 29, 711, DateTimeKind.Local).AddTicks(417) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StartDate",
                table: "Employees");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 13, 23, 11, 33, 249, DateTimeKind.Local).AddTicks(2321), "$2a$11$E5vewuSVNL2m2pheIQgt.OK69uOJ9tWJWV4Na2xcQUtytwkZRdulu", new DateTime(2023, 8, 13, 23, 11, 33, 249, DateTimeKind.Local).AddTicks(2301) });
        }
    }
}
