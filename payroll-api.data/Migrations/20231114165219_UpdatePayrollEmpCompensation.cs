﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePayrollEmpCompensation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "PayrollEmployeeCompensation",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "EngName",
                table: "PayrollEmployeeCompensation",
                type: "nvarchar(5)",
                maxLength: 5,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 14, 23, 52, 19, 215, DateTimeKind.Local).AddTicks(3660), "$2a$11$jhtSrKUVT9dStltCOY1Zmu8B0Xfdlhe914/RjwiKbaZy8vGCCeAN.", new DateTime(2023, 11, 14, 23, 52, 19, 215, DateTimeKind.Local).AddTicks(3632) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "PayrollEmployeeCompensation",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "EngName",
                table: "PayrollEmployeeCompensation",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(5)",
                oldMaxLength: 5);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 14, 23, 47, 56, 499, DateTimeKind.Local).AddTicks(5850), "$2a$11$GQgnOGq4hKGkkl0E5kiKqO4dCcHF6aomS.R9hLx7JzeR2lnI9dVRe", new DateTime(2023, 11, 14, 23, 47, 56, 499, DateTimeKind.Local).AddTicks(5829) });
        }
    }
}
