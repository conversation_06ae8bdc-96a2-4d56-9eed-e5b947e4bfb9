﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class UpdateDefaultIncomeAndOutcomeData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 2,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 3,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 4,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 5,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 6,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 7,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 8,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 9,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 10,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 11,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 12,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Outcomes",
                keyColumn: "Id",
                keyValue: 3,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Outcomes",
                keyColumn: "Id",
                keyValue: 4,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Outcomes",
                keyColumn: "Id",
                keyValue: 5,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Outcomes",
                keyColumn: "Id",
                keyValue: 6,
                column: "Active",
                value: false);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 25, 0, 3, 29, 18, DateTimeKind.Local).AddTicks(3589), "$2a$11$/FjKsrUufJImSLVD6pLeNOQmo5paginjd7fK8XCnMvRJVgcMprN6O", new DateTime(2023, 11, 25, 0, 3, 29, 18, DateTimeKind.Local).AddTicks(3570) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 2,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 3,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 4,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 5,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 6,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 7,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 8,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 9,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 10,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 11,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Incomes",
                keyColumn: "Id",
                keyValue: 12,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Outcomes",
                keyColumn: "Id",
                keyValue: 3,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Outcomes",
                keyColumn: "Id",
                keyValue: 4,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Outcomes",
                keyColumn: "Id",
                keyValue: 5,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Outcomes",
                keyColumn: "Id",
                keyValue: 6,
                column: "Active",
                value: true);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 24, 23, 42, 39, 960, DateTimeKind.Local).AddTicks(5387), "$2a$11$WBIhopsQ/1PZbuc1.Lcja.lrLVvZ63S2pcAvZ6MoBRzvm0Z4G3fgS", new DateTime(2023, 11, 24, 23, 42, 39, 960, DateTimeKind.Local).AddTicks(5371) });
        }
    }
}
