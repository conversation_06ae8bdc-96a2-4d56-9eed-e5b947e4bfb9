﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddUserTypeToUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "UserType",
                table: "Users",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 21, 1, 18, 54, 240, DateTimeKind.Local).AddTicks(4419), "$2a$11$msTXd5moj0J3kJCq/0eTaezGZI.lx8ofbpM2ty/AHeXzZYZIqCNHq", new DateTime(2023, 8, 21, 1, 18, 54, 240, DateTimeKind.Local).AddTicks(4407) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserType",
                table: "Users");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 8, 19, 16, 19, 24, 636, DateTimeKind.Local).AddTicks(6514), "$2a$11$kr3yThvvFAyihs29DWg.r.OUQZwMZrSkw0PHql2sYb3HsYAhOPhQq", new DateTime(2023, 8, 19, 16, 19, 24, 636, DateTimeKind.Local).AddTicks(6504) });
        }
    }
}
