﻿using System;
using System.Reflection;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class SeedDepartments : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var projectDir = Directory.GetParent(Assembly.GetExecutingAssembly().Location).FullName;
            migrationBuilder.Sql(File.ReadAllText(
                Path.Combine(projectDir, "SQL", "SeedDepartments.sql"
            )));
        }
        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
           
        }
    }
}
