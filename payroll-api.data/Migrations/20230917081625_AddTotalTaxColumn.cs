﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddTotalTaxColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "TotalTax",
                table: "Payrolls",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 17, 15, 16, 25, 215, DateTimeKind.Local).AddTicks(7006), "$2a$11$Gth2m0vbjrTbzmBO6d83QeM8930Kj4fbd3j1UVz9hyJSMy5lq.Iou", new DateTime(2023, 9, 17, 15, 16, 25, 215, DateTimeKind.Local).AddTicks(6996) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TotalTax",
                table: "Payrolls");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 16, 22, 28, 21, 101, DateTimeKind.Local).AddTicks(1834), "$2a$11$RFoDe8GXKmbeGhHMT4CGful5K9Fx2lVOYpsMyGi9tkYu4HNYxIYL2", new DateTime(2023, 9, 16, 22, 28, 21, 101, DateTimeKind.Local).AddTicks(1810) });
        }
    }
}
