﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using payroll_api.data;

#nullable disable

namespace payroll_api.data.Migrations
{
    [DbContext(typeof(PayrollContext))]
    [Migration("20230813161133_ReAddEmployeeAddressModel")]
    partial class ReAddEmployeeAddressModel
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("ID");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("DepartmentID")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Phone")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<decimal>("Salary")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.HasKey("ID");

                    b.HasIndex("DepartmentID");

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("payroll_api.domain.EmployeeAddress", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("District")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("EmployeeID")
                        .HasColumnType("int");

                    b.Property<string>("Province")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SubDistrict")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("ID");

                    b.HasIndex("EmployeeID")
                        .IsUnique();

                    b.ToTable("EmployeeAddresses");
                });

            modelBuilder.Entity("payroll_api.domain.Income", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("EngName")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("ID");

                    b.ToTable("Incomes");

                    b.HasData(
                        new
                        {
                            ID = 1,
                            Active = true,
                            EngName = "SAL",
                            Name = "เงินเดือน"
                        },
                        new
                        {
                            ID = 2,
                            Active = true,
                            EngName = "COM",
                            Name = "ส่วนเเบ่งการขาย"
                        },
                        new
                        {
                            ID = 3,
                            Active = true,
                            EngName = "OT",
                            Name = "ค่าล่วงเวลา"
                        },
                        new
                        {
                            ID = 4,
                            Active = true,
                            EngName = "ALW",
                            Name = "ค่าเบี้ยเลี้ยง"
                        },
                        new
                        {
                            ID = 5,
                            Active = true,
                            EngName = "DALW",
                            Name = "เบี้ยขยัน"
                        },
                        new
                        {
                            ID = 6,
                            Active = true,
                            EngName = "BFD",
                            Name = "สวัสดิการค่าอาหาร"
                        },
                        new
                        {
                            ID = 7,
                            Active = true,
                            EngName = "BHR",
                            Name = "สวัสดิการค่าเช่าบ้าน"
                        },
                        new
                        {
                            ID = 8,
                            Active = true,
                            EngName = "BTS",
                            Name = "สวัสดิการค่าเดินทาง"
                        },
                        new
                        {
                            ID = 9,
                            Active = true,
                            EngName = "BMP",
                            Name = "สวัสดิการค่าโทรศัพท์"
                        },
                        new
                        {
                            ID = 10,
                            Active = true,
                            EngName = "BCL",
                            Name = "สวัสดิการค่าครองชีพ"
                        },
                        new
                        {
                            ID = 11,
                            Active = true,
                            EngName = "POS",
                            Name = "ค่าตำแหน่ง"
                        },
                        new
                        {
                            ID = 12,
                            Active = true,
                            EngName = "BNS",
                            Name = "โบนัส"
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Outcome", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("EngName")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("ID");

                    b.ToTable("Outcomes");

                    b.HasData(
                        new
                        {
                            ID = 1,
                            Active = true,
                            EngName = "WHT",
                            Name = "ภาษีหัก ณ ที่จ่าย"
                        },
                        new
                        {
                            ID = 2,
                            Active = true,
                            EngName = "SSF",
                            Name = "ประกันสังคม"
                        },
                        new
                        {
                            ID = 3,
                            Active = true,
                            EngName = "LWP",
                            Name = "ลาไม่รับเงินเดือน"
                        },
                        new
                        {
                            ID = 4,
                            Active = true,
                            EngName = "AB",
                            Name = "ขาด/สาย"
                        },
                        new
                        {
                            ID = 5,
                            Active = true,
                            EngName = "FN",
                            Name = "ค่าปรับ"
                        },
                        new
                        {
                            ID = 6,
                            Active = true,
                            EngName = "PF",
                            Name = "กองทุนสำรองเลี้ยงชีพ"
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("DepartmentID")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("ID");

                    b.HasIndex("DepartmentID");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("payroll_api.domain.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByIp")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReasonRevoked")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ReplacedByToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("Revoked")
                        .HasColumnType("datetime2");

                    b.Property<string>("RevokedByIp")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserID")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserID");

                    b.ToTable("RefreshToken");
                });

            modelBuilder.Entity("payroll_api.domain.Setting", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserID")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("UserID");

                    b.ToTable("Settings");

                    b.HasData(
                        new
                        {
                            ID = 1,
                            Name = "CompanyName",
                            Type = "General",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 2,
                            Name = "CompanyPassportID",
                            Type = "General",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 3,
                            Name = "Email",
                            Type = "Contact",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 4,
                            Name = "Phone",
                            Type = "Contact",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 5,
                            Name = "Fax",
                            Type = "Contact",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 6,
                            Name = "Website",
                            Type = "Contact",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 7,
                            Name = "EnableSocialSecurity",
                            Type = "SocialSecurity",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 8,
                            Name = "SocialSecurityPassportID",
                            Type = "SocialSecurity",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 9,
                            Name = "Address",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 10,
                            Name = "Zipcode",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 11,
                            Name = "District",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 12,
                            Name = "SubDistrict",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 13,
                            Name = "Province",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 14,
                            Name = "Logo",
                            Type = "Logo",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 15,
                            Name = "SalaryDate",
                            Type = "Salary",
                            UserID = 1,
                            Value = ""
                        });
                });

            modelBuilder.Entity("payroll_api.domain.User", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ID");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            ID = 1,
                            Email = "<EMAIL>",
                            ExpireDate = new DateTime(3022, 8, 13, 23, 11, 33, 249, DateTimeKind.Local).AddTicks(2321),
                            Name = "Smudger",
                            PasswordHash = "$2a$11$E5vewuSVNL2m2pheIQgt.OK69uOJ9tWJWV4Na2xcQUtytwkZRdulu",
                            StartDate = new DateTime(2023, 8, 13, 23, 11, 33, 249, DateTimeKind.Local).AddTicks(2301)
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.HasOne("payroll_api.domain.Department", "Department")
                        .WithMany("Employees")
                        .HasForeignKey("DepartmentID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("payroll_api.domain.EmployeeAddress", b =>
                {
                    b.HasOne("payroll_api.domain.Employee", "Employee")
                        .WithOne("Address")
                        .HasForeignKey("payroll_api.domain.EmployeeAddress", "EmployeeID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.HasOne("payroll_api.domain.Department", "Department")
                        .WithMany("Positions")
                        .HasForeignKey("DepartmentID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("payroll_api.domain.RefreshToken", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Setting", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Settings")
                        .HasForeignKey("UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.Navigation("Employees");

                    b.Navigation("Positions");
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.Navigation("Address")
                        .IsRequired();
                });

            modelBuilder.Entity("payroll_api.domain.User", b =>
                {
                    b.Navigation("RefreshTokens");

                    b.Navigation("Settings");
                });
#pragma warning restore 612, 618
        }
    }
}
