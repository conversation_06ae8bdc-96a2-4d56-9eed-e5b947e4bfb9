﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class EditPaymentMethodToPayroll : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "PayMethod",
                table: "Payrolls",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 23, 14, 1, 31, 850, DateTimeKind.Local).AddTicks(6305), "$2a$11$jsQ7dVgpQaLPbzGLvMm5z.5mNSwAg.cgJPZ1SQjIhtqjryw68wDgu", new DateTime(2023, 10, 23, 14, 1, 31, 850, DateTimeKind.Local).AddTicks(6287) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "PayMethod",
                table: "Payrolls",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 23, 13, 58, 35, 627, DateTimeKind.Local).AddTicks(4608), "$2a$11$9jG/20L2eTstexXMgiYls.zRf5DdjbKvUmMgw9EKVuzKJkQ2K9b9K", new DateTime(2023, 10, 23, 13, 58, 35, 627, DateTimeKind.Local).AddTicks(4594) });
        }
    }
}
