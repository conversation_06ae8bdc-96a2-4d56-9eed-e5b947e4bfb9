﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using payroll_api.data;

#nullable disable

namespace payroll_api.data.Migrations
{
    [DbContext(typeof(PayrollContext))]
    [Migration("20230916152443_DropOldPayrollEmployeePK")]
    partial class DropOldPayrollEmployeePK
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("EmployeeType")
                        .HasColumnType("int");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Phone")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int?>("PositionId")
                        .HasColumnType("int");

                    b.Property<decimal>("Salary")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("PositionId");

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("payroll_api.domain.EmployeeAddress", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("District")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<string>("Province")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SubDistrict")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.ToTable("EmployeeAddresses");
                });

            modelBuilder.Entity("payroll_api.domain.Income", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("EngName")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Incomes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Active = true,
                            EngName = "SAL",
                            Name = "เงินเดือน"
                        },
                        new
                        {
                            Id = 2,
                            Active = true,
                            EngName = "COM",
                            Name = "ส่วนเเบ่งการขาย"
                        },
                        new
                        {
                            Id = 3,
                            Active = true,
                            EngName = "OT",
                            Name = "ค่าล่วงเวลา"
                        },
                        new
                        {
                            Id = 4,
                            Active = true,
                            EngName = "ALW",
                            Name = "ค่าเบี้ยเลี้ยง"
                        },
                        new
                        {
                            Id = 5,
                            Active = true,
                            EngName = "DALW",
                            Name = "เบี้ยขยัน"
                        },
                        new
                        {
                            Id = 6,
                            Active = true,
                            EngName = "BFD",
                            Name = "สวัสดิการค่าอาหาร"
                        },
                        new
                        {
                            Id = 7,
                            Active = true,
                            EngName = "BHR",
                            Name = "สวัสดิการค่าเช่าบ้าน"
                        },
                        new
                        {
                            Id = 8,
                            Active = true,
                            EngName = "BTS",
                            Name = "สวัสดิการค่าเดินทาง"
                        },
                        new
                        {
                            Id = 9,
                            Active = true,
                            EngName = "BMP",
                            Name = "สวัสดิการค่าโทรศัพท์"
                        },
                        new
                        {
                            Id = 10,
                            Active = true,
                            EngName = "BCL",
                            Name = "สวัสดิการค่าครองชีพ"
                        },
                        new
                        {
                            Id = 11,
                            Active = true,
                            EngName = "POS",
                            Name = "ค่าตำแหน่ง"
                        },
                        new
                        {
                            Id = 12,
                            Active = true,
                            EngName = "BNS",
                            Name = "โบนัส"
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Outcome", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("EngName")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Outcomes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Active = true,
                            EngName = "WHT",
                            Name = "ภาษีหัก ณ ที่จ่าย"
                        },
                        new
                        {
                            Id = 2,
                            Active = true,
                            EngName = "SSF",
                            Name = "ประกันสังคม"
                        },
                        new
                        {
                            Id = 3,
                            Active = true,
                            EngName = "LWP",
                            Name = "ลาไม่รับเงินเดือน"
                        },
                        new
                        {
                            Id = 4,
                            Active = true,
                            EngName = "AB",
                            Name = "ขาด/สาย"
                        },
                        new
                        {
                            Id = 5,
                            Active = true,
                            EngName = "FN",
                            Name = "ค่าปรับ"
                        },
                        new
                        {
                            Id = 6,
                            Active = true,
                            EngName = "PF",
                            Name = "กองทุนสำรองเลี้ยงชีพ"
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Payroll", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("PayDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalIncome")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalOutcome")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalSalary")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalSocialSecurityFund")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("Payrolls");
                });

            modelBuilder.Entity("payroll_api.domain.PayrollEmployee", b =>
                {
                    b.Property<int>("PayrollEmployeeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PayrollEmployeeId"));

                    b.Property<decimal>("Commission")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<decimal>("Overtime")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PayrollId")
                        .HasColumnType("int");

                    b.Property<decimal>("Salary")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SocialSecurityFund")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Tax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalIncome")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalOutcome")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("PayrollEmployeeId");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("PayrollId");

                    b.ToTable("PayrollEmployees");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("payroll_api.domain.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByIp")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReasonRevoked")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ReplacedByToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("Revoked")
                        .HasColumnType("datetime2");

                    b.Property<string>("RevokedByIp")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshToken");
                });

            modelBuilder.Entity("payroll_api.domain.Setting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Settings");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "CompanyName",
                            Type = "General",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 2,
                            Name = "CompanyPassportID",
                            Type = "General",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 3,
                            Name = "Email",
                            Type = "Contact",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 4,
                            Name = "Phone",
                            Type = "Contact",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 5,
                            Name = "Fax",
                            Type = "Contact",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 6,
                            Name = "Website",
                            Type = "Contact",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 7,
                            Name = "EnableSocialSecurity",
                            Type = "SocialSecurity",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 8,
                            Name = "SocialSecurityPassportID",
                            Type = "SocialSecurity",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 9,
                            Name = "Address",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 10,
                            Name = "Zipcode",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 11,
                            Name = "District",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 12,
                            Name = "SubDistrict",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 13,
                            Name = "Province",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 14,
                            Name = "Logo",
                            Type = "Logo",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 15,
                            Name = "SalaryDate",
                            Type = "Salary",
                            UserId = 1,
                            Value = ""
                        });
                });

            modelBuilder.Entity("payroll_api.domain.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Email = "<EMAIL>",
                            ExpireDate = new DateTime(3022, 9, 16, 22, 24, 42, 672, DateTimeKind.Local).AddTicks(2079),
                            Name = "Smudger",
                            PasswordHash = "$2a$11$votbTcCCqC4QCnluXjccS.5aKcXeKghmcWo/XC3gVVKA0YCRF4qQW",
                            StartDate = new DateTime(2023, 9, 16, 22, 24, 42, 672, DateTimeKind.Local).AddTicks(2054),
                            UserType = 0
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.HasOne("payroll_api.domain.Department", "Department")
                        .WithMany("Employees")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("payroll_api.domain.Position", "Position")
                        .WithMany("Employees")
                        .HasForeignKey("PositionId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Department");

                    b.Navigation("Position");
                });

            modelBuilder.Entity("payroll_api.domain.EmployeeAddress", b =>
                {
                    b.HasOne("payroll_api.domain.Employee", "Employee")
                        .WithOne("Address")
                        .HasForeignKey("payroll_api.domain.EmployeeAddress", "EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("payroll_api.domain.PayrollEmployee", b =>
                {
                    b.HasOne("payroll_api.domain.Employee", "Employee")
                        .WithMany("PayrollEmployees")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("payroll_api.domain.Payroll", "Payroll")
                        .WithMany("PayrollEmployees")
                        .HasForeignKey("PayrollId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Payroll");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.HasOne("payroll_api.domain.Department", "Department")
                        .WithMany("Positions")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("payroll_api.domain.RefreshToken", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Setting", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Settings")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.Navigation("Employees");

                    b.Navigation("Positions");
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.Navigation("Address")
                        .IsRequired();

                    b.Navigation("PayrollEmployees");
                });

            modelBuilder.Entity("payroll_api.domain.Payroll", b =>
                {
                    b.Navigation("PayrollEmployees");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.Navigation("Employees");
                });

            modelBuilder.Entity("payroll_api.domain.User", b =>
                {
                    b.Navigation("RefreshTokens");

                    b.Navigation("Settings");
                });
#pragma warning restore 612, 618
        }
    }
}
