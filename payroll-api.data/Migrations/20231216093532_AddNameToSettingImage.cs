﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddNameToSettingImage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "Id",
                keyValue: 14);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "SettingImages",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 16, 16, 35, 31, 963, DateTimeKind.Local).AddTicks(2690), "$2a$11$a6O6brIqca0bfpZgCUlWjuw.RWnb3UeY14iVPXypLq7C24IhBcbXu", new DateTime(2023, 12, 16, 16, 35, 31, 963, DateTimeKind.Local).AddTicks(2650) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Name",
                table: "SettingImages");

            migrationBuilder.InsertData(
                table: "Settings",
                columns: new[] { "Id", "Name", "Type", "UserId", "Value" },
                values: new object[] { 14, "Logo", "Logo", 1, "" });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 16, 15, 47, 0, 181, DateTimeKind.Local).AddTicks(3450), "$2a$11$uTKm/w0VVU.IX2vaYt/qCe/2n4RteedGgKurkbN352hVqkFWEtxYe", new DateTime(2023, 12, 16, 15, 47, 0, 181, DateTimeKind.Local).AddTicks(3410) });
        }
    }
}
