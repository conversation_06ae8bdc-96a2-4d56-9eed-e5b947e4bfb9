﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class SeedSettingData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Value",
                table: "Settings",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.InsertData(
                table: "Settings",
                columns: new[] { "ID", "Name", "Type", "UserID", "Value" },
                values: new object[,]
                {
                    { 1, "CompanyName", "General", 1, "" },
                    { 2, "CompanyPassportID", "General", 1, "" },
                    { 3, "Email", "Contact", 1, "" },
                    { 4, "Phone", "Contact", 1, "" },
                    { 5, "Fax", "Contact", 1, "" },
                    { 6, "Website", "Contact", 1, "" },
                    { 7, "EnableSocialSecurity", "SocialSecurity", 1, "" },
                    { 8, "SocialSecurityPassportId", "SocialSecurity", 1, "" },
                    { 9, "Address", "Address", 1, "" },
                    { 10, "Zipcode", "Address", 1, "" },
                    { 11, "District", "Address", 1, "" },
                    { 12, "SubDistrict", "Address", 1, "" },
                    { 13, "Province", "Address", 1, "" },
                    { 14, "Logo", "Logo", 1, "" },
                    { 15, "SalaryDate", "Salary", 1, "" }
                });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "StartDate" },
                values: new object[] { new DateTime(3022, 7, 18, 0, 43, 0, 683, DateTimeKind.Local).AddTicks(3814), new DateTime(2023, 7, 18, 0, 43, 0, 683, DateTimeKind.Local).AddTicks(3804) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 12);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 13);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 14);

            migrationBuilder.DeleteData(
                table: "Settings",
                keyColumn: "ID",
                keyValue: 15);

            migrationBuilder.AlterColumn<string>(
                name: "Value",
                table: "Settings",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "StartDate" },
                values: new object[] { new DateTime(3022, 7, 17, 22, 16, 34, 738, DateTimeKind.Local).AddTicks(1771), new DateTime(2023, 7, 17, 22, 16, 34, 738, DateTimeKind.Local).AddTicks(1759) });
        }
    }
}
