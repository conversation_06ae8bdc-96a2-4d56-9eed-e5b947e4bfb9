﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class EditBankAccountConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "BankAccount",
                table: "PayrollEmployees",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "Bank",
                table: "PayrollEmployees",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 3, 1, 56, 19, 490, DateTimeKind.Local).AddTicks(7515), "$2a$11$UAIpBR0NSGobEEwAOPof9OhFVJblnP2Ce8oTZZrc3xGbNqNW4tXyu", new DateTime(2023, 12, 3, 1, 56, 19, 490, DateTimeKind.Local).AddTicks(7493) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "BankAccount",
                table: "PayrollEmployees",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(10)",
                oldMaxLength: 10);

            migrationBuilder.AlterColumn<string>(
                name: "Bank",
                table: "PayrollEmployees",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 3, 1, 54, 4, 48, DateTimeKind.Local).AddTicks(8110), "$2a$11$jO8mpOloJZaZt.JYfUPDgeIc/mj96Drcp6kwsHyPH2oISlSvwJoZO", new DateTime(2023, 12, 3, 1, 54, 4, 48, DateTimeKind.Local).AddTicks(8096) });
        }
    }
}
