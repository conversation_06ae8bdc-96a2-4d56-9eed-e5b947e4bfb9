﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRefreshTokenModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 7, 28, 12, 51, 58, 795, DateTimeKind.Local).AddTicks(7162), "$2a$11$5vgDvIOjKl7rX8Oi3iTN7.3izGlvZTVkZZKICuKS/tvNn1njnlwju", new DateTime(2023, 7, 28, 12, 51, 58, 795, DateTimeKind.Local).AddTicks(7152) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 7, 28, 12, 16, 6, 742, DateTimeKind.Local).AddTicks(8275), "", new DateTime(2023, 7, 28, 12, 16, 6, 742, DateTimeKind.Local).AddTicks(8262) });
        }
    }
}
