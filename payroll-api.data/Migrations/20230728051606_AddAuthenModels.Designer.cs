﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using payroll_api.data;

#nullable disable

namespace payroll_api.data.Migrations
{
    [DbContext(typeof(PayrollContext))]
    [Migration("20230728051606_AddAuthenModels")]
    partial class AddAuthenModels
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("ID");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("DepartmentID")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.HasKey("ID");

                    b.HasIndex("DepartmentID");

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("DepartmentID")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("ID");

                    b.HasIndex("DepartmentID");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("payroll_api.domain.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByIp")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReasonRevoked")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReplacedByToken")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("Revoked")
                        .HasColumnType("datetime2");

                    b.Property<string>("RevokedByIp")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("UserID")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserID");

                    b.ToTable("RefreshToken");
                });

            modelBuilder.Entity("payroll_api.domain.Setting", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserID")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.HasIndex("UserID");

                    b.ToTable("Settings");

                    b.HasData(
                        new
                        {
                            ID = 1,
                            Name = "CompanyName",
                            Type = "General",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 2,
                            Name = "CompanyPassportID",
                            Type = "General",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 3,
                            Name = "Email",
                            Type = "Contact",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 4,
                            Name = "Phone",
                            Type = "Contact",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 5,
                            Name = "Fax",
                            Type = "Contact",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 6,
                            Name = "Website",
                            Type = "Contact",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 7,
                            Name = "EnableSocialSecurity",
                            Type = "SocialSecurity",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 8,
                            Name = "SocialSecurityPassportID",
                            Type = "SocialSecurity",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 9,
                            Name = "Address",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 10,
                            Name = "Zipcode",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 11,
                            Name = "District",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 12,
                            Name = "SubDistrict",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 13,
                            Name = "Province",
                            Type = "Address",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 14,
                            Name = "Logo",
                            Type = "Logo",
                            UserID = 1,
                            Value = ""
                        },
                        new
                        {
                            ID = 15,
                            Name = "SalaryDate",
                            Type = "Salary",
                            UserID = 1,
                            Value = ""
                        });
                });

            modelBuilder.Entity("payroll_api.domain.User", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ID");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            ID = 1,
                            Email = "<EMAIL>",
                            ExpireDate = new DateTime(3022, 7, 28, 12, 16, 6, 742, DateTimeKind.Local).AddTicks(8275),
                            Name = "Smudger",
                            PasswordHash = "",
                            StartDate = new DateTime(2023, 7, 28, 12, 16, 6, 742, DateTimeKind.Local).AddTicks(8262)
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.HasOne("payroll_api.domain.Department", "Department")
                        .WithMany("Employees")
                        .HasForeignKey("DepartmentID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.HasOne("payroll_api.domain.Department", "Department")
                        .WithMany("Positions")
                        .HasForeignKey("DepartmentID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("payroll_api.domain.RefreshToken", b =>
                {
                    b.HasOne("payroll_api.domain.User", null)
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserID");
                });

            modelBuilder.Entity("payroll_api.domain.Setting", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Settings")
                        .HasForeignKey("UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.Navigation("Employees");

                    b.Navigation("Positions");
                });

            modelBuilder.Entity("payroll_api.domain.User", b =>
                {
                    b.Navigation("RefreshTokens");

                    b.Navigation("Settings");
                });
#pragma warning restore 612, 618
        }
    }
}
