﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using payroll_api.data;

#nullable disable

namespace payroll_api.data.Migrations
{
    [DbContext(typeof(PayrollContext))]
    [Migration("20240730183138_RemoveViewEmployeeList")]
    partial class RemoveViewEmployeeList
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Bank")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("BankAccount")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("EmployeeType")
                        .HasColumnType("int");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSocialSecurity")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Phone")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int?>("PositionId")
                        .HasColumnType("int");

                    b.Property<decimal>("Salary")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("PositionId");

                    b.HasIndex("UserId");

                    b.ToTable("Employees");
                });

            modelBuilder.Entity("payroll_api.domain.EmployeeAddress", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("District")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<string>("Province")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SubDistrict")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.ToTable("EmployeeAddresses");
                });

            modelBuilder.Entity("payroll_api.domain.Income", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("EngName")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("SystemGenerate")
                        .HasColumnType("bit");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Incomes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Active = true,
                            EngName = "SAL",
                            Name = "เงินเดือน",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 2,
                            Active = false,
                            EngName = "COM",
                            Name = "ส่วนเเบ่งการขาย",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 3,
                            Active = false,
                            EngName = "OT",
                            Name = "ค่าล่วงเวลา",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 4,
                            Active = false,
                            EngName = "ALW",
                            Name = "ค่าเบี้ยเลี้ยง",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 5,
                            Active = false,
                            EngName = "DALW",
                            Name = "เบี้ยขยัน",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 6,
                            Active = false,
                            EngName = "BFD",
                            Name = "สวัสดิการค่าอาหาร",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 7,
                            Active = false,
                            EngName = "BHR",
                            Name = "สวัสดิการค่าเช่าบ้าน",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 8,
                            Active = false,
                            EngName = "BTS",
                            Name = "สวัสดิการค่าเดินทาง",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 9,
                            Active = false,
                            EngName = "BMP",
                            Name = "สวัสดิการค่าโทรศัพท์",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 10,
                            Active = false,
                            EngName = "BCL",
                            Name = "สวัสดิการค่าครองชีพ",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 11,
                            Active = false,
                            EngName = "POS",
                            Name = "ค่าตำแหน่ง",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 12,
                            Active = false,
                            EngName = "BNS",
                            Name = "โบนัส",
                            SystemGenerate = true,
                            UserId = 1
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Outcome", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("EngName")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("SystemGenerate")
                        .HasColumnType("bit");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Outcomes");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Active = true,
                            EngName = "WHT",
                            Name = "ภาษีหัก ณ ที่จ่าย",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 2,
                            Active = true,
                            EngName = "SSF",
                            Name = "ประกันสังคม",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 3,
                            Active = false,
                            EngName = "LWP",
                            Name = "ลาไม่รับเงินเดือน",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 4,
                            Active = false,
                            EngName = "AB",
                            Name = "ขาด/สาย",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 5,
                            Active = false,
                            EngName = "FN",
                            Name = "ค่าปรับ",
                            SystemGenerate = true,
                            UserId = 1
                        },
                        new
                        {
                            Id = 6,
                            Active = false,
                            EngName = "PF",
                            Name = "กองทุนสำรองเลี้ยงชีพ",
                            SystemGenerate = true,
                            UserId = 1
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.Payroll", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("PayDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PayMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("SocialSecurityFundEmployer")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalIncome")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalOutcome")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalSalary")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalSocialSecurityFund")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalTax")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Payrolls");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollEmployee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Bank")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("BankAccount")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<int>("PayrollId")
                        .HasColumnType("int");

                    b.Property<decimal>("Salary")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("SocialSecurityFund")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("Tax")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalIncome")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalOutcome")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("PayrollId");

                    b.ToTable("PayrollEmployees");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollEmployeeIncome", b =>
                {
                    b.Property<int>("PayrollEmployeeIncomeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PayrollEmployeeIncomeId"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("EngName")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("PayrollEmployeeId")
                        .HasColumnType("int");

                    b.HasKey("PayrollEmployeeIncomeId");

                    b.HasIndex("PayrollEmployeeId");

                    b.ToTable("PayrollEmployeeIncomes");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollEmployeeOutcome", b =>
                {
                    b.Property<int>("PayrollEmployeeOutcomeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PayrollEmployeeOutcomeId"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("EngName")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("PayrollEmployeeId")
                        .HasColumnType("int");

                    b.HasKey("PayrollEmployeeOutcomeId");

                    b.HasIndex("PayrollEmployeeId");

                    b.ToTable("PayrollEmployeeOutcomes");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(600)
                        .HasColumnType("nvarchar(600)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("PayrollId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PayrollId");

                    b.HasIndex("UserId");

                    b.ToTable("PayrollHistories");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("Positions");
                });

            modelBuilder.Entity("payroll_api.domain.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByIp")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReasonRevoked")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ReplacedByToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("Revoked")
                        .HasColumnType("datetime2");

                    b.Property<string>("RevokedByIp")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshToken");
                });

            modelBuilder.Entity("payroll_api.domain.Settings.Setting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Settings");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "CompanyName",
                            Type = "General",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 2,
                            Name = "CompanyPassportID",
                            Type = "General",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 3,
                            Name = "Email",
                            Type = "Contact",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 4,
                            Name = "Phone",
                            Type = "Contact",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 5,
                            Name = "Fax",
                            Type = "Contact",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 6,
                            Name = "Website",
                            Type = "Contact",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 7,
                            Name = "EnableSocialSecurity",
                            Type = "SocialSecurity",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 8,
                            Name = "SocialSecurityPassportID",
                            Type = "SocialSecurity",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 9,
                            Name = "Address",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 10,
                            Name = "Zipcode",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 11,
                            Name = "District",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 12,
                            Name = "SubDistrict",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 13,
                            Name = "Province",
                            Type = "Address",
                            UserId = 1,
                            Value = ""
                        },
                        new
                        {
                            Id = 15,
                            Name = "SalaryDueDate",
                            Type = "Salary",
                            UserId = 1,
                            Value = ""
                        });
                });

            modelBuilder.Entity("payroll_api.domain.Settings.SettingImage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<byte[]>("Content")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("SettingImages");
                });

            modelBuilder.Entity("payroll_api.domain.TutorialStep", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("TutorialSteps");
                });

            modelBuilder.Entity("payroll_api.domain.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("PackageType")
                        .HasColumnType("int");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Email = "<EMAIL>",
                            ExpireDate = new DateTime(3023, 7, 31, 1, 31, 38, 133, DateTimeKind.Local).AddTicks(9940),
                            FirstName = "วนศักดิ์",
                            LastName = "สุเรนทรางกูร",
                            PackageType = 0,
                            PasswordHash = "$2a$11$6Jg9.ok6ognF2hrx4U1AculVVidl7dD6HSPl1uatSAYI4qcL2./zO",
                            StartDate = new DateTime(2024, 7, 31, 1, 31, 38, 133, DateTimeKind.Local).AddTicks(9830),
                            UserType = 0,
                            Username = "Smudger"
                        });
                });

            modelBuilder.Entity("payroll_api.domain.UserTutorialStep", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("TutorialStepId")
                        .HasColumnType("int");

                    b.HasKey("Id", "UserId", "TutorialStepId");

                    b.HasIndex("TutorialStepId");

                    b.HasIndex("UserId");

                    b.ToTable("UserTutorialSteps");
                });

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Departments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.HasOne("payroll_api.domain.Department", "Department")
                        .WithMany("Employees")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("payroll_api.domain.Position", "Position")
                        .WithMany("Employees")
                        .HasForeignKey("PositionId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Employees")
                        .HasForeignKey("UserId");

                    b.Navigation("Department");

                    b.Navigation("Position");

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.EmployeeAddress", b =>
                {
                    b.HasOne("payroll_api.domain.Employee", "Employee")
                        .WithOne("Address")
                        .HasForeignKey("payroll_api.domain.EmployeeAddress", "EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Employee");
                });

            modelBuilder.Entity("payroll_api.domain.Income", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Incomes")
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Outcome", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Outcomes")
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.Payroll", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Payrolls")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollEmployee", b =>
                {
                    b.HasOne("payroll_api.domain.Employee", "Employee")
                        .WithMany("PayrollEmployees")
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("payroll_api.domain.Payrolls.Payroll", "Payroll")
                        .WithMany("PayrollEmployees")
                        .HasForeignKey("PayrollId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Employee");

                    b.Navigation("Payroll");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollEmployeeIncome", b =>
                {
                    b.HasOne("payroll_api.domain.Payrolls.PayrollEmployee", "PayrollEmployee")
                        .WithMany("payrollEmployeeIncomes")
                        .HasForeignKey("PayrollEmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PayrollEmployee");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollEmployeeOutcome", b =>
                {
                    b.HasOne("payroll_api.domain.Payrolls.PayrollEmployee", "PayrollEmployee")
                        .WithMany("PayrollEmployeeOutcomes")
                        .HasForeignKey("PayrollEmployeeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PayrollEmployee");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollHistory", b =>
                {
                    b.HasOne("payroll_api.domain.Payrolls.Payroll", "Payroll")
                        .WithMany("PayrollHistories")
                        .HasForeignKey("PayrollId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("PayrollHistories")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Payroll");

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.HasOne("payroll_api.domain.Department", "Department")
                        .WithMany("Positions")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("payroll_api.domain.RefreshToken", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Settings.Setting", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("Settings")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Settings.SettingImage", b =>
                {
                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("SettingImages")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.UserTutorialStep", b =>
                {
                    b.HasOne("payroll_api.domain.TutorialStep", "TutorialStep")
                        .WithMany("UserTutorialSteps")
                        .HasForeignKey("TutorialStepId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("payroll_api.domain.User", "User")
                        .WithMany("UserTutorialSteps")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TutorialStep");

                    b.Navigation("User");
                });

            modelBuilder.Entity("payroll_api.domain.Department", b =>
                {
                    b.Navigation("Employees");

                    b.Navigation("Positions");
                });

            modelBuilder.Entity("payroll_api.domain.Employee", b =>
                {
                    b.Navigation("Address")
                        .IsRequired();

                    b.Navigation("PayrollEmployees");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.Payroll", b =>
                {
                    b.Navigation("PayrollEmployees");

                    b.Navigation("PayrollHistories");
                });

            modelBuilder.Entity("payroll_api.domain.Payrolls.PayrollEmployee", b =>
                {
                    b.Navigation("PayrollEmployeeOutcomes");

                    b.Navigation("payrollEmployeeIncomes");
                });

            modelBuilder.Entity("payroll_api.domain.Position", b =>
                {
                    b.Navigation("Employees");
                });

            modelBuilder.Entity("payroll_api.domain.TutorialStep", b =>
                {
                    b.Navigation("UserTutorialSteps");
                });

            modelBuilder.Entity("payroll_api.domain.User", b =>
                {
                    b.Navigation("Departments");

                    b.Navigation("Employees");

                    b.Navigation("Incomes");

                    b.Navigation("Outcomes");

                    b.Navigation("PayrollHistories");

                    b.Navigation("Payrolls");

                    b.Navigation("RefreshTokens");

                    b.Navigation("SettingImages");

                    b.Navigation("Settings");

                    b.Navigation("UserTutorialSteps");
                });
#pragma warning restore 612, 618
        }
    }
}
