﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddDueDateToPayroll : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "DueDate",
                table: "Payrolls",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 12, 3, 0, 53, 45, 678, DateTimeKind.Local).AddTicks(485), "$2a$11$FV/eyx4wSu0rNfLirN.fsuHFw1xk8xhgdxVHrpus3nIygziUD.tne", new DateTime(2023, 12, 3, 0, 53, 45, 678, DateTimeKind.Local).AddTicks(469) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DueDate",
                table: "Payrolls");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 30, 15, 44, 18, 685, DateTimeKind.Local).AddTicks(2914), "$2a$11$6VZEe1H4fJ7UsYyoTHuTYON5rQa/4VU9b/.cyJZcLIMtJWzx2l88q", new DateTime(2023, 11, 30, 15, 44, 18, 685, DateTimeKind.Local).AddTicks(2904) });
        }
    }
}
