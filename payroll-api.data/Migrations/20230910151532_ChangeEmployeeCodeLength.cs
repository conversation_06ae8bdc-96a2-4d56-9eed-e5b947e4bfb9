﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class ChangeEmployeeCodeLength : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "Employees",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 10, 22, 15, 31, 356, DateTimeKind.Local).AddTicks(3481), "$2a$11$t6iv0WX0xKPEr9xT2C2Fx.joPVzRQBsAC7I8FkusF5Kog4GKCbghW", new DateTime(2023, 9, 10, 22, 15, 31, 356, DateTimeKind.Local).AddTicks(3463) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "Employees",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(20)",
                oldMaxLength: 20);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 10, 17, 4, 22, 926, DateTimeKind.Local).AddTicks(2459), "$2a$11$oE0SgygB/xjtbiuHiRBvM.Jh6JLM1qN9AajKxnNrZ61dtp32YAmpm", new DateTime(2023, 9, 10, 17, 4, 22, 926, DateTimeKind.Local).AddTicks(2444) });
        }
    }
}
