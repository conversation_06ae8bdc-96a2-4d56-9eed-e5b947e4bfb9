﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddPayrollEmployeeIncomeAndOutcome : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PayrollEmployeeCompensation");

            migrationBuilder.CreateTable(
                name: "PayrollEmployeeIncomes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    EngName = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: false),
                    PayrollEmployeeId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollEmployeeIncomes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PayrollEmployeeIncomes_PayrollEmployees_PayrollEmployeeId",
                        column: x => x.PayrollEmployeeId,
                        principalTable: "PayrollEmployees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayrollEmployeeOutcomes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    EngName = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: false),
                    PayrollEmployeeId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollEmployeeOutcomes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PayrollEmployeeOutcomes_PayrollEmployees_PayrollEmployeeId",
                        column: x => x.PayrollEmployeeId,
                        principalTable: "PayrollEmployees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 30, 0, 12, 49, 643, DateTimeKind.Local).AddTicks(277), "$2a$11$0VXNP5bLg/S96YPK.2Qj0OeqtYYPcTkBgpecMIZ3eZPuz0q9W8rnO", new DateTime(2023, 11, 30, 0, 12, 49, 643, DateTimeKind.Local).AddTicks(256) });

            migrationBuilder.CreateIndex(
                name: "IX_PayrollEmployeeIncomes_PayrollEmployeeId",
                table: "PayrollEmployeeIncomes",
                column: "PayrollEmployeeId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollEmployeeOutcomes_PayrollEmployeeId",
                table: "PayrollEmployeeOutcomes",
                column: "PayrollEmployeeId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PayrollEmployeeIncomes");

            migrationBuilder.DropTable(
                name: "PayrollEmployeeOutcomes");

            migrationBuilder.CreateTable(
                name: "PayrollEmployeeCompensation",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PayrollEmployeeId = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EngName = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollEmployeeCompensation", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PayrollEmployeeCompensation_PayrollEmployees_PayrollEmployeeId",
                        column: x => x.PayrollEmployeeId,
                        principalTable: "PayrollEmployees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 27, 23, 19, 6, 66, DateTimeKind.Local).AddTicks(8484), "$2a$11$aOV6MngzoyVzh5l5Md66Ve846GV2VKh0gAVfBhord10D/VpDqcEEu", new DateTime(2023, 11, 27, 23, 19, 6, 66, DateTimeKind.Local).AddTicks(8457) });

            migrationBuilder.CreateIndex(
                name: "IX_PayrollEmployeeCompensation_PayrollEmployeeId",
                table: "PayrollEmployeeCompensation",
                column: "PayrollEmployeeId");
        }
    }
}
