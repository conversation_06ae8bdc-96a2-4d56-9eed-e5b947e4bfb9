﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddPayrollHistory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PayrollHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(600)", maxLength: 600, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UserId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PayrollHistories_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 1, 15, 30, 7, 408, DateTimeKind.Local).AddTicks(9833), "$2a$11$XjpX4XlfwNkFUD4.daPnveSyZEMbfXQu4CG.4Av2f0z85tzubQuku", new DateTime(2023, 10, 1, 15, 30, 7, 408, DateTimeKind.Local).AddTicks(9823) });

            migrationBuilder.CreateIndex(
                name: "IX_PayrollHistories_UserId",
                table: "PayrollHistories",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PayrollHistories");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 9, 17, 15, 16, 25, 215, DateTimeKind.Local).AddTicks(7006), "$2a$11$Gth2m0vbjrTbzmBO6d83QeM8930Kj4fbd3j1UVz9hyJSMy5lq.Iou", new DateTime(2023, 9, 17, 15, 16, 25, 215, DateTimeKind.Local).AddTicks(6996) });
        }
    }
}
