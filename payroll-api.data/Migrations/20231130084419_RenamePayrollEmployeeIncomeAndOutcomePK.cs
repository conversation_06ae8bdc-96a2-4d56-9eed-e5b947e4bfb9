﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class RenamePayrollEmployeeIncomeAndOutcomePK : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Id",
                table: "PayrollEmployeeOutcomes",
                newName: "PayrollEmployeeOutcomeId");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "PayrollEmployeeIncomes",
                newName: "PayrollEmployeeIncomeId");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 30, 15, 44, 18, 685, DateTimeKind.Local).AddTicks(2914), "$2a$11$6VZEe1H4fJ7UsYyoTHuTYON5rQa/4VU9b/.cyJZcLIMtJWzx2l88q", new DateTime(2023, 11, 30, 15, 44, 18, 685, DateTimeKind.Local).AddTicks(2904) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PayrollEmployeeOutcomeId",
                table: "PayrollEmployeeOutcomes",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "PayrollEmployeeIncomeId",
                table: "PayrollEmployeeIncomes",
                newName: "Id");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 11, 30, 0, 12, 49, 643, DateTimeKind.Local).AddTicks(277), "$2a$11$0VXNP5bLg/S96YPK.2Qj0OeqtYYPcTkBgpecMIZ3eZPuz0q9W8rnO", new DateTime(2023, 11, 30, 0, 12, 49, 643, DateTimeKind.Local).AddTicks(256) });
        }
    }
}
