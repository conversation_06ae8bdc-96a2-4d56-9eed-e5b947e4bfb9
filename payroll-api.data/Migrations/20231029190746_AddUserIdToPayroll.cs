﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AddUserIdToPayroll : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "UserId",
                table: "Payrolls",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 30, 2, 7, 46, 125, DateTimeKind.Local).AddTicks(6101), "$2a$11$0njTeTN4VW9XmayEwaeyde6BNVJiiaQr4kBU3u/G.P/HOlc1YAB2y", new DateTime(2023, 10, 30, 2, 7, 46, 125, DateTimeKind.Local).AddTicks(6085) });

            migrationBuilder.CreateIndex(
                name: "IX_Payrolls_UserId",
                table: "Payrolls",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Payrolls_Users_UserId",
                table: "Payrolls",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payrolls_Users_UserId",
                table: "Payrolls");

            migrationBuilder.DropIndex(
                name: "IX_Payrolls_UserId",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "Payrolls");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 10, 23, 14, 1, 31, 850, DateTimeKind.Local).AddTicks(6305), "$2a$11$jsQ7dVgpQaLPbzGLvMm5z.5mNSwAg.cgJPZ1SQjIhtqjryw68wDgu", new DateTime(2023, 10, 23, 14, 1, 31, 850, DateTimeKind.Local).AddTicks(6287) });
        }
    }
}
