﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace payroll_api.data.Migrations
{
    /// <inheritdoc />
    public partial class AppNavigationToRefreshTokenModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RefreshToken_Users_UserID",
                table: "RefreshToken");

            migrationBuilder.AlterColumn<int>(
                name: "UserID",
                table: "RefreshToken",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 7, 28, 13, 17, 0, 494, DateTimeKind.Local).AddTicks(5465), "$2a$11$XnagUuKlA5x1ELVjRUPO6eKy7pQ56VomkfkrlQWCTA3JzKcu3aExC", new DateTime(2023, 7, 28, 13, 17, 0, 494, DateTimeKind.Local).AddTicks(5454) });

            migrationBuilder.AddForeignKey(
                name: "FK_RefreshToken_Users_UserID",
                table: "RefreshToken",
                column: "UserID",
                principalTable: "Users",
                principalColumn: "ID",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RefreshToken_Users_UserID",
                table: "RefreshToken");

            migrationBuilder.AlterColumn<int>(
                name: "UserID",
                table: "RefreshToken",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "ID",
                keyValue: 1,
                columns: new[] { "ExpireDate", "PasswordHash", "StartDate" },
                values: new object[] { new DateTime(3022, 7, 28, 12, 53, 27, 488, DateTimeKind.Local).AddTicks(7526), "$2a$11$c0RXXTK0xUIISNscc6Y9hOhXMN8/ePA/b1k.cS.YzKyCi9BynRQda", new DateTime(2023, 7, 28, 12, 53, 27, 488, DateTimeKind.Local).AddTicks(7515) });

            migrationBuilder.AddForeignKey(
                name: "FK_RefreshToken_Users_UserID",
                table: "RefreshToken",
                column: "UserID",
                principalTable: "Users",
                principalColumn: "ID");
        }
    }
}
