﻿CREATE OR ALTER PROCEDURE DeleteUserData
	@UserId INT
AS
BEGIN
	IF EXISTS (SELECT 1 FROM Users WHERE Id = @UserId)
	BEGIN
		DELETE FROM Settings WHERE UserId = @UserId
		--DELETE FROM SettingImages WHERE UserId = @UserId
		DELETE FROM Payrolls WHERE UserId = @UserId
		--DELETE FROM PayrollHistories WHERE UserId = @UserId
		DELETE FROM Incomes WHERE UserId = @UserId
		DELETE FROM Outcomes WHERE UserId = @UserId
		DELETE FROM Departments WHERE UserId = @UserId
		DELETE FROM Employees WHERE UserId = @UserId
		DELETE FROM RefreshToken WHERE UserId = @UserId
		DELETE FROM Users WHERE Id = @UserId
	END
END
