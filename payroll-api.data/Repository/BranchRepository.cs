﻿using Dapper;
using payroll_api.data.Infrastructure;
using payroll_api.domain;
using payroll_api.utility.ViewModel;

namespace payroll_api.data.Repository
{
    public class BranchRepository : EntityBaseRepository<Branch>, IBranchRepository
    {
        private readonly DapperContext _dapperContext;

        public BranchRepository(IDbFactory dbFactory, DapperContext dapper) : base(dbFactory)
        {
            _dapperContext = dapper ?? throw new ArgumentNullException(nameof(dapper));
        }

        public async Task<BranchDetailViewModel> GetBranch(int id)
        {
            using var connection = _dapperContext.CreateConnection();
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT 
                    b.Id, b.Name, b.Code, b.Active,
                    ba.Address, ba.SubDistrict, ba.District, ba.ZipCode, ba.Province
                FROM Branchs b INNER JOIN BranchAddresses ba on ba.BranchId = b.Id
                WHERE b.Id = @BranchId
            ";

            var result = await conn.QueryAsync<BranchDetailViewModel, AddressViewModel, BranchDetailViewModel>(
                sql,
                (e, ea) =>
                {
                    e.Address = ea;
                    return e;
                },
                new { BranchId = id },
                splitOn: "Address"
            );

            return result.FirstOrDefault();
        }

        public async Task<bool> IsExistBranchByUserIdAsync(int userId)
        {
            using var connection = _dapperContext.CreateConnection();
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT 1
                FROM Branchs WITH (NOLOCK)
                WHERE UserId = @UserId AND Active = 1
            ";

            var result = await conn.QueryFirstOrDefaultAsync<int?>(sql, new { UserId = userId });

            return result is not null;

        }
    }
}
