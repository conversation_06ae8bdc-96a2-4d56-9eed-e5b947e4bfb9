﻿using System.Reflection;
using Dapper;
using payroll_api.data.Infrastructure;
using payroll_api.domain.Settings;
using payroll_api.utility.ViewModel;

namespace payroll_api.data.Repository
{
    public class SettingRepository : EntityBaseRepository<Setting>, ISettingRepository
    {
        private readonly DapperContext _dapperContext;

        public SettingRepository(IDbFactory dbFactory, DapperContext dapperContext) : base(dbFactory)
        {
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
        }

        public async Task<AppSettingViewModel> GetAllSettings(int userId)
        {
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT Name, Value
                FROM Settings WITH (NOLOCK)
                WHERE UserId = @UserId
            ";

            var settings = (await conn.QueryAsync<SettingViewModel>(sql, new { UserId = userId })).ToList();

            var result = new AppSettingViewModel();
            var resultType = typeof(AppSettingViewModel);

            foreach (var setting in settings)
            {
                var property = resultType.GetProperty(setting.Name, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (property is not null && property.CanWrite)
                {
                    property.SetValue(result, setting.Value);
                }
            }

            return result;
        }


        public async Task<AppSettingViewModel> GetSettingByNames(int userId, params string[] names)
        {
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT Name, Value
                FROM Settings WITH (NOLOCK)
                WHERE Name IN @Names AND UserId = @UserId
            ";

            var settings = (await conn.QueryAsync<SettingViewModel>(sql, new { Names = names, UserId = userId })).ToList();

            var result = new AppSettingViewModel();
            var resultType = typeof(AppSettingViewModel);

            foreach (var setting in settings)
            {
                var property = resultType.GetProperty(setting.Name, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (property is not null && property.CanWrite)
                {
                    property.SetValue(result, setting.Value);
                }
            }

            return result;
        }
    }
}
