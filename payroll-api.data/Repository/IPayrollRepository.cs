using payroll_api.domain.Payrolls;
using payroll_api.utility.ViewModel;

namespace payroll_api.data.Repository
{
    public interface IPayrollRepository : IEntityBaseRepository<Payroll>
    {
        Task<IList<PayrollEmployeeExcelViewModel>> GetPayrollEmployeeExcel(int id);
        Task<PayrollExcelViewModel> GetPayrollExcel(int payrollId);
        Task<string?> GetLatestCode(int userId, int startMonth = 0);
        Task<bool> IsExistCode(string code, int userId);
        Task<bool> IsExistPayrollInCurrentMonth(int month, int year, int userId);
    }
}
