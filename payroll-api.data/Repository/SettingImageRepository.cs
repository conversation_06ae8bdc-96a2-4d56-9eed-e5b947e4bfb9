﻿using payroll_api.data.Infrastructure;
using payroll_api.domain.Settings;

namespace payroll_api.data.Repository
{
    public interface ISettingImageRepository : IEntityBaseRepository<SettingImage>
    {

    }
    public class SettingImageRepository : EntityBaseRepository<SettingImage>, ISettingImageRepository
    {
        public SettingImageRepository(IDbFactory dbFactory) : base(dbFactory)
        {
        }
    }
}
