using Dapper;
using payroll_api.data.Infrastructure;
using payroll_api.domain;
using payroll_api.utility.ViewModel;

namespace payroll_api.data.Repository
{
    public class UserRepository : EntityBaseRepository<User>, IUserRepository
    {
        private readonly IDbFactory _dbFactory;
        private readonly DapperContext _dapperContext;

        public UserRepository(IDbFactory dbFactory, DapperContext dapperContext) : base(dbFactory)
        {
            _dbFactory = dbFactory ?? throw new ArgumentNullException(nameof(dbFactory));
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
        }

        public async Task<UserViewModel> GetUser(int id)
        {
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT Id, FirstName, LastName, Email, StartDate, ExpireDate
                FROM Users
                WHERE Id = @UserId
            ";

            var result = await conn.QueryFirstOrDefaultAsync<UserViewModel>(sql, new { UserId = id });

            return result;
        }

        public async Task<int> GetUserIdByEmail(string email)
        {
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT Id
                FROM Users
                WHERE Email = @Email
            ";

            return await conn.QueryFirstOrDefaultAsync<int>(sql, new { Email = email });
        }

        public async Task<bool> IsVerifiedUser(string email)
        {
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT 1
                FROM Users
                WHERE Email = @Email AND IsEmailVerified = 1
            ";

            return await conn.QueryFirstOrDefaultAsync<bool>(sql, new { Email = email });
        }
    }
}
