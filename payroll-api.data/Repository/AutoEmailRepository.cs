using Dapper;
using payroll_api.data.Infrastructure;
using payroll_api.domain;
using payroll_api.utility.Enum;

namespace payroll_api.data.Repository
{
    public class AutoEmailRepository : EntityBaseRepository<AutoEmail>, IAutoEmailRepository
    {
        private readonly DapperContext _dapperContext;

        public AutoEmailRepository(IDbFactory dbFactory, DapperContext dapperContext) : base(dbFactory)
        {
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
        }

        public async Task<IEnumerable<AutoEmail>> GetEmailByTemplate(EmailTemplateType emailTemplateType)
        {
            using var conn = _dapperContext.CreateConnection();

            var sql = @"
                SELECT Id, Subject, Body, [To]
                FROM AutoEmails
                WHERE EmailTemplateType = @TemplateType
            ";

            return await conn.QueryAsync<AutoEmail>(sql, new { TemplateType = emailTemplateType });
        }
    }
}
