﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain;

namespace payroll_api.data.Configuration
{
    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            builder.HasKey(e => e.Id);


            builder.Property(e => e.FirstName)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(e => e.LastName)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(e => e.StartDate).IsRequired();

            builder.Property(e => e.ExpireDate).IsRequired();

            builder.Property(e => e.UserType)
                .IsRequired()
                .HasDefaultValue(0);

            builder.HasMany(u => u.Incomes)
                .WithOne(i => i.User);

            builder.HasMany(u => u.Outcomes)
                .WithOne(o => o.User);

            builder.HasMany(u => u.Employees)
                .WithOne(e => e.User);
        }
    }
}
