﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain.Payrolls;

namespace payroll_api.data.Configuration
{
    public class PayrollConfiguration : IEntityTypeConfiguration<Payroll>
    {
        public void Configure(EntityTypeBuilder<Payroll> builder)
        {
            builder.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(e => e.PayMethod)
                .IsRequired(false)
                .HasMaxLength(50);

            builder.HasOne(p => p.User)
                .WithMany(u => u.Payrolls)
                .HasForeignKey(p => p.UserId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.Property(e => e.Total).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.TotalIncome).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.TotalOutcome).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.TotalSalary).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.TotalSocialSecurityFund).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.TotalTax).HasColumnType("decimal(18, 2)");

            builder.Property(e => e.PayDate)
                .IsRequired(false);

            builder.Property(e => e.SocialSecurityFundEmployer)
                .HasColumnType("decimal(18, 2)");
        }
    }
}
