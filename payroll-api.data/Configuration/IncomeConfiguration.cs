﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain;

namespace payroll_api.data.Configuration
{
    public class IncomeConfiguration : IEntityTypeConfiguration<Income>
    {
        public void Configure(EntityTypeBuilder<Income> builder)
        {
            builder.HasKey(e => e.Id);

            builder.Property(e => e.EngName).HasMaxLength(5);

            builder.Property(e => e.Name).HasMaxLength(100);

            builder.HasData(
                new Income { Id = 1, SystemGenerate = true, Name = "เงินเดือน", EngName = "SAL", Active = true, UserId = 1 },
                new Income { Id = 2, SystemGenerate = true, Name = "ส่วนเเบ่งการขาย", EngName = "COM", Active = false, UserId = 1 },
                new Income { Id = 3, SystemGenerate = true, Name = "ค่าล่วงเวลา", EngName = "OT", Active = false, UserId = 1 },
                new Income { Id = 4, SystemGenerate = true, Name = "ค่าเบี้ยเลี้ยง", EngName = "ALW", Active = false, UserId = 1 },
                new Income { Id = 5, SystemGenerate = true, Name = "เบี้ยขยัน", EngName = "DALW", Active = false, UserId = 1 },
                new Income { Id = 6, SystemGenerate = true, Name = "สวัสดิการค่าอาหาร", EngName = "BFD", Active = false, UserId = 1 },
                new Income { Id = 7, SystemGenerate = true, Name = "สวัสดิการค่าเช่าบ้าน", EngName = "BHR", Active = false, UserId = 1 },
                new Income { Id = 8, SystemGenerate = true, Name = "สวัสดิการค่าเดินทาง", EngName = "BTS", Active = false, UserId = 1 },
                new Income { Id = 9, SystemGenerate = true, Name = "สวัสดิการค่าโทรศัพท์", EngName = "BMP", Active = false, UserId = 1 },
                new Income { Id = 10, SystemGenerate = true, Name = "สวัสดิการค่าครองชีพ", EngName = "BCL", Active = false, UserId = 1 },
                new Income { Id = 11, SystemGenerate = true, Name = "ค่าตำแหน่ง", EngName = "POS", Active = false, UserId = 1 },
                new Income { Id = 12, SystemGenerate = true, Name = "โบนัส", EngName = "BNS", Active = false, UserId = 1 }
            );
        }
    }
}
