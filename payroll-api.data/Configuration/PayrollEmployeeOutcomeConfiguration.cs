﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain.Payrolls;

namespace payroll_api.data.Configuration
{
    public class PayrollEmployeeOutcomeConfiguration : IEntityTypeConfiguration<PayrollEmployeeOutcome>
    {
        public void Configure(EntityTypeBuilder<PayrollEmployeeOutcome> builder)
        {
            builder.Property(e => e.Name).IsRequired().HasMaxLength(100);
            builder.Property(e => e.EngName).IsRequired().HasMaxLength(5);
            builder.Property(e => e.Amount).HasColumnType("decimal(18, 2)");
        }
    }
}
