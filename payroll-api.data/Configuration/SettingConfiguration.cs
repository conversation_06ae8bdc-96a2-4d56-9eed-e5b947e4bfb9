﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain.Settings;
using payroll_api.utility.Constant;

namespace payroll_api.data.Configuration
{
    public class SettingConfiguration : IEntityTypeConfiguration<Setting>
    {
        public void Configure(EntityTypeBuilder<Setting> builder)
        {
            builder.HasKey(e=>e.Id);

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(e => e.Type)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(e => e.Value)
                .IsRequired(false);

            builder.HasOne(e => e.User)
                .WithMany(e=>e.Settings)
                .IsRequired();

            builder.HasData(
                new Setting { Id = 1, Name = SettingKey.CompanyName, Type = SettingTypeKey.General, UserId = 1, Value = "" },
                new Setting { Id = 2, Name = SettingKey.CompanyPassportID, Type = SettingTypeKey.General, UserId = 1, Value = "" },
                new Setting { Id = 3, Name = SettingKey.Email, Type = SettingTypeKey.Contact, UserId = 1, Value = "" },
                new Setting { Id = 4, Name = SettingKey.Phone, Type = SettingTypeKey.Contact, UserId = 1, Value = "" },
                new Setting { Id = 5, Name = SettingKey.Fax, Type = SettingTypeKey.Contact, UserId = 1, Value = "" },
                new Setting { Id = 6, Name = SettingKey.Website, Type = SettingTypeKey.Contact, UserId = 1, Value = "" },
                new Setting { Id = 7, Name = SettingKey.EnableSocialSecurity, Type = SettingTypeKey.SocialSecurity, UserId = 1, Value = "" },
                new Setting { Id = 8, Name = SettingKey.SocialSecurityPassportID, Type = SettingTypeKey.SocialSecurity, UserId = 1, Value = "" },
                new Setting { Id = 9, Name = SettingKey.Address, Type = SettingTypeKey.Address, UserId = 1, Value = "" },
                new Setting { Id = 10, Name = SettingKey.Zipcode, Type = SettingTypeKey.Address, UserId = 1, Value = "" },
                new Setting { Id = 11, Name = SettingKey.District, Type = SettingTypeKey.Address, UserId = 1, Value = "" },
                new Setting { Id = 12, Name = SettingKey.SubDistrict, Type = SettingTypeKey.Address, UserId = 1, Value = "" },
                new Setting { Id = 13, Name = SettingKey.Province, Type = SettingTypeKey.Address, UserId = 1, Value = "" },
                // new Setting { Id = 14, Name = SettingKey.Logo, Type = SettingTypeKey.Logo, UserId = 1, Value = "" },
                new Setting { Id = 15, Name = SettingKey.SalaryDueDate, Type = SettingTypeKey.Salary, UserId = 1, Value = "" }
            );
        }
    }
}
