﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain;

namespace payroll_api.data.Configuration
{
    public class EmployeeConfiguration : IEntityTypeConfiguration<Employee>
    {
        public void Configure(EntityTypeBuilder<Employee> builder)
        {
            builder.HasKey(e => e.Id);

            builder.Property(e => e.FirstName)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(e => e.LastName)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(100)
                .IsRequired(false);

            builder.Property(e => e.Phone)
                .IsRequired()
                .HasMaxLength(10)
                .IsRequired(false);

            builder.Property(e => e.Salary)
                .IsRequired()
                .HasColumnType("decimal(18, 2)")
                .HasDefaultValue(0);

            builder.Property(e => e.Code)
                .HasMaxLength(20)
                .IsRequired();

            builder.Property(e => e.Bank)
                .HasMaxLength(20)
                .IsRequired(false);

            builder.Property(e => e.BankAccount)
                .HasMaxLength(10)
                .IsRequired(false);

            builder.HasOne(e => e.Position)
                .WithMany(p => p.Employees)
                .HasForeignKey(e => e.PositionId)
                .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
