﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain;

namespace payroll_api.data.Configuration
{
    public class RefreshTokenConfiguration : IEntityTypeConfiguration<RefreshToken>
    {
        public void Configure(EntityTypeBuilder<RefreshToken> builder)
        {
            builder.Property(e => e.ReasonRevoked).IsRequired(false).HasMaxLength(100);

            builder.Property(e => e.CreatedByIp).HasMaxLength(50);

            builder.Property(e => e.RevokedByIp).IsRequired(false).HasMaxLength(50);

            builder.Property(e => e.ReplacedByToken).IsRequired(false);
        }
    }
}
