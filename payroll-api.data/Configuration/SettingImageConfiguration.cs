using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain.Settings;

namespace payroll_api.data.Configuration
{
    public class SettingImageConfiguration : IEntityTypeConfiguration<SettingImage>
    {
        public void Configure(EntityTypeBuilder<SettingImage> builder)
        {
            builder.HasKey(e => e.Id);

            builder.HasOne(e => e.User)
                .WithMany(e => e.SettingImages);

            builder.Property(e => e.Name)
                .HasMaxLength(50)
                .IsRequired();
        }
    }
}
