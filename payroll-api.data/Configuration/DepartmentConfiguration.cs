﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain;

namespace payroll_api.data.Configuration
{
    public class DepartmentConfiguration : IEntityTypeConfiguration<Department>
    {
        public void Configure(EntityTypeBuilder<Department> builder)
        {
            builder.HasKey(e => e.Id);

            builder.Property(e => e.Code)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(e => e.Name)
                .IsRequired()
                .HasMaxLength(50);

            builder.HasMany(e => e.Positions)
                .WithOne(e => e.Department);

            builder.HasMany(e => e.Employees)
                .WithOne(e => e.Department);

            builder.HasOne(e => e.User)
                .WithMany(e => e.Departments);
        }
    }
}
