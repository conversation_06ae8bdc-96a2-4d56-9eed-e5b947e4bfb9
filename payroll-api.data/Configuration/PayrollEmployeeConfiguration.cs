﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using payroll_api.domain.Payrolls;

namespace payroll_api.data.Configuration
{
    public class PayrollEmployeeConfiguration : IEntityTypeConfiguration<PayrollEmployee>
    {
        public void Configure(EntityTypeBuilder<PayrollEmployee> builder)
        {
            builder.<PERSON><PERSON><PERSON>(e => e.Id);

            builder.HasOne(e => e.Employee)
                .WithMany(e => e.PayrollEmployees)
                .HasForeignKey(e => e.EmployeeId);

            builder.HasOne(e => e.Payroll)
                .WithMany(e => e.PayrollEmployees)
                .HasForeignKey(e => e.PayrollId);

            builder.HasMany(pe => pe.payrollEmployeeIncomes)
                .WithOne(pei => pei.PayrollEmployee)
                .HasForeignKey(pei => pei.PayrollEmployeeId);

            builder.HasMany(pe => pe.PayrollEmployeeOutcomes)
                .WithOne(peo => peo.PayrollEmployee)
                .HasForeignKey(peo => peo.PayrollEmployeeId);

            builder.Property(e => e.Total).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.TotalIncome).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.TotalOutcome).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.Salary).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.SocialSecurityFund).HasColumnType("decimal(18, 2)");
            builder.Property(e => e.Tax).HasColumnType("decimal(18, 2)");

            builder.Property(e => e.Bank)
                .HasMaxLength(50)
                .IsRequired(false);

            builder.Property(e => e.BankAccount)
                .HasMaxLength(10)
                .IsRequired(false);

            builder.Property(e => e.PositionName)
                .HasMaxLength(50)
                .IsRequired(false);
        }
    }
}
