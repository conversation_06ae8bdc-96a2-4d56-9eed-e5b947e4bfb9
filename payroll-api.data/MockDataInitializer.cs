﻿using payroll_api.domain;

namespace payroll_api.data
{
    public static class MockDataInitializer
    {
        public static List<Department> GenerateDepartments()
        {
            return new List<Department>() { 
                new Department() { Code = "DEP00001", Name = "บริหาร", Active = true },
                new Department() { Code = "DEP00002", Name = "จัดการสนับสนุน", Active = true },
                new Department() { Code = "DEP00003", Name = "การขาย", Active = true },
                new Department() { Code = "DEP00004", Name = "การตลาด", Active = true },
                new Department() { Code = "DEP00005", Name = "บัญชี", Active = true },
                new Department() { Code = "DEP00006", Name = "บุคคล", Active = true },
                new Department() { Code = "DEP00007", Name = "ผลิต", Active = true },
                new Department() { Code = "DEP00008", Name = "ปฏิบัติการ", Active = true },
                new Department() { Code = "DEP00009", Name = "ไอที", Active = true },
            };
        }
    }
}
