﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>payroll_api.data</RootNamespace>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="SQL\SeedDepartments.sql" />
    <None Remove="SQL\SeedPositions.sql" />
    <None Remove="SQL\SPDeleteUserData.sql" />
    <None Remove="StoreProcedure\spUpdateEmployeeCode.sql" />
    <None Remove="Views\viewEmployeeList.sql" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="SQL\SPDeleteUserData.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SQL\SeedPositions.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SQL\SeedDepartments.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="StoreProcedure\spUpdateEmployeeCode.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Views\viewEmployeeList.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="EntityFramework" Version="6.5.1" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\payroll-api.domain\payroll-api.domain.csproj" />
    <ProjectReference Include="..\payroll-api.utility\payroll-api.utility.csproj" />
  </ItemGroup>

</Project>
