CREATE OR ALTER VIEW dbo.viewEmployeeList
AS
    SELECT
        e.Id,
        e.FirstName + ' ' + e.LastName AS FullName,
        e.Code,
        e.PositionId,
        e.<PERSON>,
        e.UserId,
        d.Name AS Department,
        ISNULL(p.Name, '') AS Position,
        CASE 
        WHEN e.EmployeeType = 1 THEN 'FullTime'
        WHEN e.EmployeeType = 2 THEN 'PartTime'
        ELSE ''
    END AS EmployeeType
    FROM Employees e
        INNER JOIN Departments d ON d.Id = e.DepartmentId
        LEFT JOIN Positions p ON p.Id = e.PositionId;
