﻿namespace payroll_api.data.Infrastructure
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly PayrollContext _context;

        public UnitOfWork(PayrollContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public void Commit()
        {
            _context.SaveChanges();
        }

        public async Task CommitAsync()
        {
            await _context.SaveChangesAsync();
        }
    }
}
