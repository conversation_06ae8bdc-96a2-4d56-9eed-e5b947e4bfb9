using AutoMapper;
using Moq;
using payroll_api.data.Infrastructure;
using payroll_api.data.Repository;
using payroll_api.domain.Payrolls;
using payroll_api.email;
using payroll_api.Infrastructure.Service;
using payroll_api.service;
using payroll_api.utility.Constant;
using payroll_api.utility.Enum;
using payroll_api.utility.ViewModel;

namespace payroll_api.tests.Services;

public class PayrollServiceTest
{
    private readonly Mock<IPayrollRepository> _mockPayrollRepository;
    private readonly Mock<IOutcomeReporitory> _mockOutcomeRepository;
    private readonly Mock<IEmployeeRepository> _mockEmployeeRepository;
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IIdentityService> _mockIdentityService;
    private readonly Mock<ISettingService> _mockSettingService;
    private readonly Mock<IIncomeService> _mockIncomeService;
    private readonly Mock<IOutcomeService> _mockOutcomeService;
    private readonly Mock<IIncomeRepository> _mockIncomeRepository;
    private readonly Mock<IReportService> _mockReportService;
    private readonly PayrollService _mockPayrollService;
    private readonly Mock<IAutoEmailRepository> _mockAutoEmailRepository;

    public PayrollServiceTest()
    {
        // Initialize mocks
        _mockPayrollRepository = new Mock<IPayrollRepository>();
        _mockOutcomeRepository = new Mock<IOutcomeReporitory>();
        _mockEmployeeRepository = new Mock<IEmployeeRepository>();
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockMapper = new Mock<IMapper>();
        _mockIdentityService = new Mock<IIdentityService>();
        _mockSettingService = new Mock<ISettingService>();
        _mockIncomeService = new Mock<IIncomeService>();
        _mockOutcomeService = new Mock<IOutcomeService>();
        _mockIncomeRepository = new Mock<IIncomeRepository>();
        _mockReportService = new Mock<IReportService>();
        _mockAutoEmailRepository = new Mock<IAutoEmailRepository>();

        // Inject mocks into the service
        _mockPayrollService = new PayrollService(
            _mockPayrollRepository.Object,
            _mockOutcomeRepository.Object,
            _mockEmployeeRepository.Object,
            _mockUnitOfWork.Object,
            _mockMapper.Object,
            _mockIdentityService.Object,
            _mockSettingService.Object,
            _mockIncomeService.Object,
            _mockOutcomeService.Object,
            _mockIncomeRepository.Object,
            _mockReportService.Object,
            _mockAutoEmailRepository.Object
        );
    }

    #region GenerateCode
    [Fact]
    public async Task GenerateCode_ShouldReturnNewCode_WhenNoExistingCode()
    {
        // Arrange
        var userId = 1;
        var year = DateTime.Now.Year;
        var month = DateTime.Now.Month;
        var expectedCode = $"PAY_{year}{month:D2}_01";

        _mockIdentityService.Setup(x => x.GetUserId()).Returns(userId);

        _mockPayrollRepository
            .Setup(repo => repo.GetLatestCode(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync("");

        // Act
        var generatedCode = await _mockPayrollService.GenerateCode();

        // Assert
        Assert.Equal(expectedCode, generatedCode);
    }
    [Fact]
    public async Task GenerateCode_ShouldIncrementRunningNumber_WhenExistingCodeFound()
    {
        // Arrange
        var userId = 1;
        var year = DateTime.Now.Year;
        var month = DateTime.Now.Month;
        var latestCode = $"PAY_{year}{month:D2}_01";
        var expectedCode = $"PAY_{year}{month:D2}_02";

        _mockIdentityService.Setup(x => x.GetUserId()).Returns(userId);

        _mockPayrollRepository
            .Setup(repo => repo.GetLatestCode(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(latestCode);

        // Act
        var generatedCode = await _mockPayrollService.GenerateCode();

        // Assert
        Assert.Equal(expectedCode, generatedCode);
    }

    #endregion

    #region CreatePayroll
    [Fact]
    public async Task CreatePayroll_ShouldReturnPayrollId_WhenPayrollCreatedSuccessfully()
    {
        // Arrange
        var userId = 1;
        var currentDate = DateTime.Now;
        var createPayrollViewModel = new CreatePayrollViewModel
        {
            Id = 0,
            Code = "PAY_2023_01_01",
            PayrollEmployees =
            [
                new() {
                    Total = 1000,
                    Tax = 100,
                    SocialSecurityFund = 50
                }
            ],
            Total = 1000,
            TotalTax = 100,
            TotalSocialSecurityFund = 50
        };
        var payroll = new Payroll
        {
            Id = 0,
            Code = "PAY_2023_01_01",
            UserId = userId,
            Status = PayrollStatus.Draft,
            PayrollHistories = [
                new PayrollHistory {
                    Name = PayrollHistoryKeys.Create
                }
            ],
        };

        _mockIdentityService
            .Setup(x => x.GetUserId())
            .Returns(userId);
        _mockMapper
            .Setup(x => x.Map<Payroll>(createPayrollViewModel))
            .Returns(payroll);

        // Act
        var result = await _mockPayrollService.CreatePayroll(createPayrollViewModel);

        // Assert
        _mockMapper.Verify(x => x.Map<Payroll>(It.IsAny<CreatePayrollViewModel>()), Times.Once);
        _mockPayrollRepository.Verify(x => x.Add(It.IsAny<Payroll>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.CommitAsync(), Times.Once);

        Assert.Equal(0, result);
        Assert.Equal(currentDate.Date, payroll.PayrollDate.Date);
        Assert.Contains(PayrollHistoryKeys.Create, payroll.PayrollHistories.Select(x => x.Name));
    }
    #endregion

    #region Update Payroll Status
    [Fact]
    public async Task CancelPayroll_ShouldUpdateStatusAndAddHistory()
    {
        // Arrange
        var userId = 1;
        var payrollId = 1;
        var payroll = new Payroll
        {
            Id = payrollId,
            Status = PayrollStatus.Approve,
            PayrollHistories = []
        };

        _mockIdentityService
            .Setup(x => x.GetUserId())
            .Returns(userId);
        _mockPayrollRepository
            .Setup(x => x.GetSingle(It.IsAny<int>()))
            .Returns(payroll);

        // Act
        await _mockPayrollService.CancelPayroll(payrollId);

        // Assert
        Assert.Equal(PayrollStatus.Cancel, payroll.Status);
        var history = payroll.PayrollHistories[0];
        Assert.Equal(PayrollHistoryKeys.Cancel, history.Name);

        _mockPayrollRepository.Verify(x => x.GetSingle(userId), Times.Once);
        _mockUnitOfWork.Verify(x => x.CommitAsync(), Times.Once);
    }


    [Fact]
    public async Task ApprovePayroll_ShouldUpdateStatusAndAddHistory()
    {
        // Arrange
        var userId = 1;
        var payrollId = 1;
        var payroll = new Payroll
        {
            Id = payrollId,
            Status = PayrollStatus.Approve,
            PayrollHistories = new List<PayrollHistory>()
        };

        _mockIdentityService
            .Setup(x => x.GetUserId())
            .Returns(userId);
        _mockPayrollRepository
            .Setup(x => x.GetSingle(It.IsAny<int>()))
            .Returns(payroll);

        // Act
        await _mockPayrollService.Approve(payrollId);

        // Assert
        Assert.Equal(PayrollStatus.Approve, payroll.Status);
        var history = payroll.PayrollHistories[0];
        Assert.Equal(PayrollHistoryKeys.Approve, history.Name);

        _mockPayrollRepository.Verify(x => x.GetSingle(userId), Times.Once);
        _mockUnitOfWork.Verify(x => x.CommitAsync(), Times.Once);
    }
    #endregion
}
