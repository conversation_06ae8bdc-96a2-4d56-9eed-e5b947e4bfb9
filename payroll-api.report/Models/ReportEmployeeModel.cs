﻿using payroll_api.utility.ViewModel;
using QuestPDF.Fluent;

namespace payroll_api.report.Models
{
    public class ReportEmployeeModel
    {
        public List<DepartmentReportModel> Departments { get; set; }
        public byte[] CompanyLogo { get; set; }
        public SettingContactViewModel CompanyInfo { get; set; }
    }
    public class DepartmentReportModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public List<EmployeeReportModel> Employee { get; set; }
    }
    public class EmployeeReportModel
    {
        public string Code { get; set; }
        public string FullName { get; set; }
        public string Position { get; set; }
        public decimal Salary { get; set; }
        public string Branch { get; set; }
    }
}
