﻿using payroll_api.report.Constants;
using payroll_api.utility.ViewModel;
using QuestPDF.Fluent;
using QuestPDF.Infrastructure;

namespace payroll_api.report.Components
{
    public class CompanyHeaderComponent : IComponent
    {
        public byte[] CompanyLogo { get; }
        public SettingContactViewModel CompanyInfo { get; }

        public CompanyHeaderComponent(
            SettingContactViewModel companyInfo
        )
        {
            CompanyInfo = companyInfo;
        }

        public CompanyHeaderComponent(
            SettingContactViewModel companyInfo,
            byte[] companyLogo
        )
        {
            CompanyInfo = companyInfo;
            CompanyLogo = companyLogo;
        }

        public void Compose(IContainer container)
        {
            container.Row(row => {
                if (CompanyLogo?.Length > 0)
                {
                    row.ConstantItem(36).PaddingRight(10).Image(CompanyLogo).FitWidth();
                }
                row.RelativeItem().Column(col =>
                {
                    col.Item().Text(CompanyInfo?.CompanyName).CustomBold();
                    if (!string.IsNullOrEmpty(CompanyInfo?.AddressInfo))
                    {
                        col.Item()
                        .DefaultTextStyle(style =>
                            style.FontSize(ReportConstant.FooterFontSize)
                        )
                        .TextGray(CompanyInfo?.AddressInfo);
                    }
                    if (!string.IsNullOrEmpty(CompanyInfo?.ContactInfo))
                    {
                        col.Item()
                        .DefaultTextStyle(style =>
                            style.FontSize(ReportConstant.FooterFontSize)
                        )
                        .TextGray(CompanyInfo?.ContactInfo);
                    }
                });
            });
        }
    }
}
