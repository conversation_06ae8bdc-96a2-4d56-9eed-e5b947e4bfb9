﻿using payroll_api.report.Components;
using payroll_api.report.Constants;
using payroll_api.report.Models;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

public class SalarySlipDocument : IDocument
{
    public ReportEmployeeModel Model { get; }

    public void Compose(IDocumentContainer container)
    {
        container.Page(page =>
        {
            ReportExtension.DefaultPageSetting(page);

            page.Header().Element(ComposeHeader);
            page.Content().Element(ComposeContent);
        });
    }

    void ComposeHeader(IContainer container)
    {
        container.Column(col => {
            col.Item().Row(row =>
            {
                row.RelativeItem(3)
                    .Column(col =>
                    {
                        col.Item().Component(new CompanyHeaderComponent(Model.CompanyInfo));
                       
                        col
                        .Item()
                        .Text("ข้อมูลพนักงาน")
                        .Bold();

                        col.Item().Row(row =>
                        {
                            row.RelativeItem()
                            .TextGray("ชื่อ-นามสกุล");
                            row.RelativeItem()
                            .TextGray("สมชาย ทดสอบ");
                        });

                        col.Item().Row(row =>
                        {
                            row.RelativeItem()
                            .TextGray("แผนก");
                            row.RelativeItem()
                            .TextGray("บัญชี");
                        });

                        col.Item().Row(row =>
                        {
                            row.RelativeItem()
                            .TextGray("ตำแหน่ง");
                            row.RelativeItem()
                            .TextGray("ผู้จัดการ");
                        });
                    });

                row.RelativeItem(2)
                    .Column(col =>
                    {
                        col.Item()
                        .Text(text =>
                        {
                            text.Line("สลิปเงินเดือน").Bold();
                            text.Span("รอบเงินเดือน").Bold();
                        });

                        col.Item().Row(row => {
                            row.RelativeItem()
                            .TextGray("ตั้งเเต่วันที่");
                            row.RelativeItem()
                            .TextGray(Placeholders.ShortDate());
                        });

                        col.Item().Row(row => {
                            row.RelativeItem()
                            .TextGray("ถึงวันที่");
                            row.RelativeItem()
                            .TextGray(Placeholders.ShortDate());
                        });

                        col.Item().Row(row => {
                            row.RelativeItem()
                            .TextGray("วันที่ชำระเงินเดือน");
                            row.RelativeItem()
                            .TextGray("");
                        });
                    });
            });

            col.Item().PaddingVertical(8).HoriZontalLine();
        });
    }

    void ComposeContent(IContainer container)
    {
        container.Column(col =>
        {
            col.Item()
            .PaddingBottom(5)
            .Text("รายการเงินเดือน: 2023/05")
            .Bold();

            col.Item()
             .DefaultTextStyle(text => text.FontColor(ReportConstant.GreyText))
             .Table(table =>
             {
                 table
                 .ColumnsDefinition(col =>
                 {
                     col.RelativeColumn();
                     col.ConstantColumn(100);
                     col.RelativeColumn();
                     col.ConstantColumn(100);
                 });

                 table.Header(header =>
                 {
                     header.Cell().Padding(4).Text("รายได้").FontColor(Colors.Black).Bold();
                     header.Cell().Padding(4).AlignRight().Text("จำนวนเงิน (บาท)").FontColor(Colors.Black).Bold();
                     header.Cell().Padding(4).Text("รายการหัก").FontColor(Colors.Black).Bold();
                     header.Cell().Padding(4).AlignRight().Text("จำนวนเงิน (บาท)").FontColor(Colors.Black).Bold();
                 });

                 table.Cell().ColumnSpan(4).PaddingVertical(4).HoriZontalLine();

                 var income = Placeholders.Random.NextDouble() * 10000;
                 foreach (var item in Enumerable.Range(1, 10))
                 {
                     table.Cell().Padding(4).Text(Placeholders.Label());
                     table.Cell().Padding(4).AlignRight().Text($"{income:F2}");
                     if (item < 5)
                     {
                         table.Cell().Padding(4).Text(Placeholders.Label());
                         table.Cell().Padding(4).AlignRight().Text($"{income:F2}");
                     }
                     else
                     {
                         table.Cell().ColumnSpan(2);
                     }
                 }

                 table.Cell().ColumnSpan(4).PaddingVertical(4).HoriZontalLine();

                 table.Cell().Padding(4).Text("รายได้รวม").FontColor(Colors.Black).Bold();
                 table.Cell().Padding(4).AlignRight().Text($"{Placeholders.Decimal():F2}").Bold();
                 table.Cell().Padding(4).Text("รายการหัก").FontColor(Colors.Black).Bold();
                 table.Cell().Padding(4).AlignRight().Text($"{Placeholders.Decimal():F2}").Bold();
                 table.Cell().ColumnSpan(2);
                 table.Cell().Padding(4).Text("เงินรับได้สุทธิ").FontSize(16).FontColor(Colors.Black).ExtraBold();
                 table.Cell().Padding(4).AlignRight().Text(Placeholders.Decimal())
                 .FontSize(16)
                 .FontColor(Colors.Black).ExtraBold();
             });

            col.Item()
                .PaddingTop(12)
                .Text("รวมเงินได้ทั้งปี: 2023")
                .Bold();

            col.Item()
                .PaddingTop(10)
                .Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                        columns.RelativeColumn();
                    });

                    table.Cell().LabelCell("เงินได้สะสม (บาท)");
                    table.Cell().LabelCell("เงินประกันสังคมสะสม");
                    table.Cell().LabelCell("ลายเซ็นผู้รับ");
                    table.Cell().ValueCell(Placeholders.Decimal());
                    table.Cell().ValueCell(Placeholders.Decimal());
                    table.Cell().ValueCell("");
                });
        });
    }
}