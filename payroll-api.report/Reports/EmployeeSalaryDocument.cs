﻿using payroll_api.report.Components;
using payroll_api.report.Constants;
using payroll_api.report.Models;
using payroll_api.utility.Helper;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace payroll_api.report.Reports
{
    public class EmployeeSalaryDocument : IDocument
    {
        public ReportEmployeeModel Model { get; }

        public void Compose(IDocumentContainer container)
        {
            container.Page(page =>
            {
                ReportExtension.DefaultPageSetting(page);

                page.Header().ShowOnce().Element(ComposeHeader);
                page.Content().Element(ComposeContent);
                page.Footer().Element(ComposeFooter);
            });
        }

        private void ComposeHeader(IContainer container)
        {
            container.Column(col =>
            {
                col.Item()
                    .Row(row =>
                    {
                        row.RelativeItem()
                            .Component(new CompanyHeaderComponent(Model.CompanyInfo));

                        row.RelativeItem()
                            .Column(col =>
                            {
                                col.Item().Text("รายงานเงินเดือน").Bold();

                                col.Item()
                                    .Row(row =>
                                    {
                                        row.RelativeItem().TextGray("รอบเงินเดือน");
                                        row.RelativeItem().TextGray(Placeholders.ShortDate());
                                    });
                            });
                    });

                col.Item().PaddingVertical(8).HoriZontalLine();
            });
        }

        private void ComposeContent(IContainer container)
        {
            container.Column(col =>
            {
                col.Item()
                .PaddingBottom(5)
                .Text("รายการเงินเดือน: 2023/05")
                .Bold();

                col.Item()
                    .DefaultTextStyle(text => text.FontColor(ReportConstant.GreyText))
                    .Table(table =>
                    {
                        table.ColumnsDefinition(col =>
                        {
                            col.ConstantColumn(30);
                            col.RelativeColumn();
                            col.RelativeColumn();
                            col.RelativeColumn();
                            col.RelativeColumn();
                        });

                        table.Header(header =>
                        {
                            header.Cell().Element(Style).Text("ลําดับ").FontColor(Colors.Black).Bold();
                            header.Cell().Element(Style).Text("ชื่อ-นามสกุล").FontColor(Colors.Black).Bold();
                            header.Cell().Element(Style).Text("แผนก").FontColor(Colors.Black).Bold();
                            header.Cell().Element(Style).Text("ตำแหน่ง").FontColor(Colors.Black).Bold();
                            header.Cell().Element(Style)
                                .AlignRight()
                                .Text("เงินเดือน").FontColor(Colors.Black).Bold();

                            IContainer Style(IContainer container)
                            {
                                return container
                                    .DefaultTextStyle(text => text.SemiBold())
                                    .BorderBottom(ReportConstant.BorderBottom)
                                    .BorderColor(Colors.Grey.Lighten1)
                                    .Padding(4);
                            }
                        });


                        foreach (var item in Enumerable.Range(1, 75))
                        {
                            table.Cell().Element(Style).Text(item.ToString());
                            table.Cell().Element(Style).Text(Placeholders.Name());
                            table.Cell().Element(Style).Text(Placeholders.Label());
                            table.Cell().Element(Style).Text(Placeholders.Label());
                            table.Cell().Element(Style).AlignRight().Text(Placeholders.Decimal());

                            IContainer Style(IContainer container)
                            {
                                return container
                                    .BorderBottom(ReportConstant.BorderBottom)
                                    .BorderColor(Colors.Grey.Lighten4)
                                    .Padding(4);
                            }

                            table.Cell()
                           .ColumnSpan(5)
                           .AlignRight()
                           .ShowIf(item == 75)
                           .Padding(4)
                           .Text($"รวม {Placeholders.Decimal()}")
                           .FontColor(Colors.Black)
                           .SemiBold();
                        }
                    });
            });
        }

        private void ComposeFooter(IContainer container)
        {
            container
                .DefaultTextStyle(style =>
                    style.FontSize(ReportConstant.FooterFontSize)
                )
                .Row(row =>
                {
                    row.RelativeItem()
                        .Text("วันที่ออกรายงาน " + DateTimeUtils.GetThaiDateAndTime(DateTime.Now));

                    row.RelativeItem()
                        .Component(new PaginationComponent());
                });
        }
    }
}
