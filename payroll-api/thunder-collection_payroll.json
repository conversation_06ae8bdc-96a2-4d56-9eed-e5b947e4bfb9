{"client": "Thunder Client", "collectionName": "payroll", "dateExported": "2023-07-18T16:01:49.870Z", "version": "1.1", "folders": [{"_id": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "department", "containerId": "", "created": "2023-07-11T18:55:08.333Z", "sortNum": 10000}, {"_id": "82962c16-f041-4e28-aeeb-17e4cb1fa37f", "name": "position", "containerId": "", "created": "2023-07-11T18:55:08.334Z", "sortNum": 20000}, {"_id": "76340acc-7205-4f1c-940b-bc83fff49e6e", "name": "employee", "containerId": "", "created": "2023-07-11T18:55:08.335Z", "sortNum": 30000}, {"_id": "383ea3f5-70a3-45f6-8b0c-095624bafd36", "name": "setting", "containerId": "", "created": "2023-07-16T16:19:58.352Z", "sortNum": 40000}], "requests": [{"_id": "86333c14-c480-4106-b9d5-066eff6ba73d", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "get_departments", "url": "https://localhost:{{PORT}}/api/Departments", "method": "GET", "sortNum": 10000, "created": "2023-07-11T18:55:08.333Z", "modified": "2023-07-16T16:25:44.815Z", "headers": [], "params": [], "tests": []}, {"_id": "1776a7d9-124a-46c8-a284-2c68a5cf0e0d", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "82962c16-f041-4e28-aeeb-17e4cb1fa37f", "name": "create_position", "url": "https://localhost:{{PORT}}/api/Positions", "method": "POST", "sortNum": 10000, "created": "2023-07-11T18:55:08.337Z", "modified": "2023-07-16T16:25:38.608Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"active\": true,\n  \"name\": \"หัวหน้า\",\n  \"departmentId\": 4\n}", "form": []}, "tests": []}, {"_id": "68cf3370-f806-47ad-8777-a11378bd0cef", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "383ea3f5-70a3-45f6-8b0c-095624bafd36", "name": "get_settings", "url": "https://localhost:{{PORT}}/api/Settings", "method": "GET", "sortNum": 10000, "created": "2023-07-16T16:20:09.512Z", "modified": "2023-07-16T16:22:04.916Z", "headers": [], "params": [], "tests": []}, {"_id": "1a183e9d-1206-4332-a987-19faf5e9f55c", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "get_department_detail", "url": "https://localhost:{{PORT}}/api/Departments/3", "method": "GET", "sortNum": 15000, "created": "2023-07-11T18:55:08.336Z", "modified": "2023-07-16T16:25:50.173Z", "headers": [], "params": [], "tests": []}, {"_id": "ad361488-6ea2-436c-b056-a60b7ce6c113", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "383ea3f5-70a3-45f6-8b0c-095624bafd36", "name": "get_readonly_settings", "url": "https://localhost:{{PORT}}/api/Settings/Readonly", "method": "GET", "sortNum": 15000, "created": "2023-07-17T18:43:05.023Z", "modified": "2023-07-17T18:43:25.044Z", "headers": [], "params": [], "tests": []}, {"_id": "18da412f-71e8-40a8-b44c-2850fe514ede", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "create_department", "url": "https://localhost:{{PORT}}/api/Departments", "method": "POST", "sortNum": 20000, "created": "2023-07-11T18:55:08.334Z", "modified": "2023-07-16T16:25:57.968Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"code\": \"CODE005\",\n  \"name\": \"บุคคล\"\n}", "form": []}, "tests": []}, {"_id": "ff62a9fd-f285-4233-b8bc-5729cf279c03", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "383ea3f5-70a3-45f6-8b0c-095624bafd36", "name": "save_settings", "url": "https://localhost:{{PORT}}/api/Settings", "method": "POST", "sortNum": 20000, "created": "2023-07-17T16:17:21.131Z", "modified": "2023-07-18T15:37:18.962Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"CompanyName\": {\n    \"id\": 1,\n    \"name\": \"CompanyName\",\n    \"value\": \"1988 Cafe\",\n    \"type\": \"General\",\n    \"userID\": 1\n  },\n  \"Email\": {\n    \"id\": 3,\n    \"name\": \"Email\",\n    \"value\": \"<EMAIL>\",\n    \"type\": \"Contact\",\n    \"userID\": 1\n  },\n  \"Website\": {\n    \"id\": 6,\n    \"name\": \"Website\",\n    \"value\": \"www.1988Cafe.com\",\n    \"type\": \"Contact\",\n    \"userID\": 1\n  }\n}", "form": []}, "tests": []}, {"_id": "a8c1c5c1-f71e-4edd-b671-0237d51e250f", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "a4aea0f8-750c-46db-bcc3-1e317fffe546", "name": "delete_department", "url": "https://localhost:{{PORT}}/api/Departments/2", "method": "DELETE", "sortNum": 30000, "created": "2023-07-11T18:55:08.335Z", "modified": "2023-07-16T16:26:08.329Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"code\": \"CODE001\",\n  \"name\": \"TEST\"\n}", "form": []}, "tests": []}, {"_id": "df6b9d3f-18b8-41bc-abad-d084e8429b39", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "76340acc-7205-4f1c-940b-bc83fff49e6e", "name": "download_excel", "url": "https://localhost:{{PORT}}/api/Employee/DownloadCreateEmployeeExcel?response_type=blob", "method": "GET", "sortNum": 40000, "created": "2023-07-11T18:55:08.338Z", "modified": "2023-07-16T16:25:25.317Z", "headers": [], "params": [{"name": "response_type", "value": "blob", "isPath": false}], "tests": []}, {"_id": "4dd2f929-8cd6-456f-8bb4-d7217deff045", "colId": "9799b8a0-f3e4-4952-b818-699373d171c5", "containerId": "76340acc-7205-4f1c-940b-bc83fff49e6e", "name": "create_employee", "url": "https://localhost:{{PORT}}/api/Employee", "method": "POST", "sortNum": 50000, "created": "2023-07-11T18:55:18.178Z", "modified": "2023-07-16T16:25:30.595Z", "headers": [], "params": [], "body": {"type": "json", "raw": "{\n  \"id\": 0,\n  \"firstName\": \"สมชาย\",\n  \"lastName\": \"สายลม\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"123456789\",\n  \"active\": true,\n  \"departmentID\": 1\n}", "form": []}, "tests": []}]}