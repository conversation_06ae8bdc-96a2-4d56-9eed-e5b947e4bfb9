﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>payroll_api</RootNamespace>
    <UserSecretsId>0a0165c7-162c-480b-b7f8-8f4c4dfa5692</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <!-- <OutputType>Library</OutputType> -->
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Upload\**" />
    <Content Remove="Upload\**" />
    <EmbeddedResource Remove="Upload\**" />
    <None Remove="Upload\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.13.1" />
    <PackageReference Include="Serilog" Version="4.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.8" />
    <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" Version="*******" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.18.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\payroll-api.data\payroll-api.data.csproj" />
    <ProjectReference Include="..\payroll-api.domain\payroll-api.domain.csproj" />
    <ProjectReference Include="..\payroll-api.email\payroll-api.email.csproj" />
    <ProjectReference Include="..\payroll-api.report\payroll-api.report.csproj" />
    <ProjectReference Include="..\payroll-api.service\payroll-api.service.csproj" />
    <ProjectReference Include="..\payroll-api.utility\payroll-api.utility.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Fonts\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Fonts\Sarabun-Bold.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Sarabun-Light.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Images\logo.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <None Update="Fonts\Sarabun-Bold.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Sarabun-Light.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Images\logo.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
