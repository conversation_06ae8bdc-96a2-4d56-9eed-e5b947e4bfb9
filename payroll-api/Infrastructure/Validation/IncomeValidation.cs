using FluentValidation;
using payroll_api.utility.ViewModel;

namespace payroll_api.Infrastructure.Validation
{
    public class IncomeValidation : AbstractValidator<IncomeViewModel>
    {
        public IncomeValidation()
        {
            RuleFor(x => x.EngName)
                .MaximumLength(5)
                .WithMessage("กรุณากรอกรหัสไม่เกิน 5 ตัวอักษร")
                .NotEmpty()
                .WithMessage("กรุณากรอกรหัส");
        }
    }
}