using FluentValidation;
using payroll_api.data.Repository;
using payroll_api.Infrastructure.Service;
using payroll_api.utility.ViewModel;

namespace payroll_api.Infrastructure.Validation
{
    public class CreatePayrolllViewModelValidator : AbstractValidator<CreatePayrollViewModel>
    {
        private readonly IPayrollRepository _payrollRepository;
        private readonly IIdentityService _identityService;

        public CreatePayrolllViewModelValidator(IPayrollRepository payrollRepository, IIdentityService identityService)
        {
            _payrollRepository = payrollRepository;
            _identityService = identityService;

            RuleFor(x => x.Total)
                .Equal(x =>
                    x.PayrollEmployees.Sum(x => x.Total)
                )
                .WithMessage("การคำนวณจำนวนเงินที่จ่ายสุทธิผิดพลาด");

            RuleFor(x => x.TotalTax)
                .Equal(x =>
                    x.PayrollEmployees.Sum(x => x.Tax)
                )
                .WithMessage("การคำนวณภาษีหัก ณ ที่จ่ายผิดพลาด");

            RuleFor(x => x.TotalSocialSecurityFund)
                .Equal(x =>
                    x.PayrollEmployees.Sum(x => x.SocialSecurityFund)
                )
                .WithMessage("การคำนวณประกันสังคมผิดพลาด");

            RuleFor(x => x.Code)
                .Must(code => !CheckExistCode(code))
                .WithMessage("รหัสนี้ถูกใช้งานเเล้ว")
                .When(x => x.Id == 0);
        }

        private bool CheckExistCode(string code)
        {
            var userId = _identityService.GetUserId();
            return _payrollRepository.IsExistCode(code, userId).GetAwaiter().GetResult();
        }
    }
}