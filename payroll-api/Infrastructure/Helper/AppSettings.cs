﻿namespace payroll_api.Infrastructure.Helper
{
    public class AppSettings
    {
        public string Secret { get; set; }
        // token expire in x minutes
        public int TokenExpire { get; set; }
        // refresh token expire in x days
        public int RefreshTokenExpire { get; set; }

        // refresh token time to live (in days), inactive tokens are
        // automatically deleted from the database after this time
        public int RefreshTokenTTL { get; set; }
    }
}
