using payroll_api.Infrastructure.BackgroundJob;
using Quartz;

namespace payroll_api.Infrastructure.Extension;

public static class DependencyInjection
{
    public static void AddQuartz(this IServiceCollection services)
    {
        services.AddQuartz(options => { });

        services.AddQuartzHostedService(
            options => options.WaitForJobsToComplete = true
        );

        services.ConfigureOptions<EmailJobSetup>();
        // services.ConfigureOptions<BackgroundLogJobSetup>();
    }
}
