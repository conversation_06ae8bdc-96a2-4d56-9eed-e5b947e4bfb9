﻿using Microsoft.EntityFrameworkCore;
using payroll_api.data;

namespace payroll_api.Infrastructure.Extension
{
    public static class MigrationManager
    {
        public static WebApplication MigrateDatabase(this WebApplication webApp)
        {
            using (var scope = webApp.Services.CreateScope())
            {
                using (var appContext = scope.ServiceProvider.GetRequiredService<PayrollContext>())
                {
                    try
                    {
                        appContext.Database.Migrate();
                    }
                    catch (System.Exception ex)
                    {
                        //Log errors or do anything you think it's needed
                        throw;
                    }
                }
            }

            return webApp;
        }
    }
}
