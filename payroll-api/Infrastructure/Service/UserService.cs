﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using payroll_api.data;
using payroll_api.domain;
using payroll_api.Infrastructure.Exception;
using payroll_api.utility.ViewModel;
using payroll_api.utility.Enum;
using payroll_api.service;
using payroll_api.service.Infrastructure.Helper;
using payroll_api.utility.Filter;
using Dapper;
using payroll_api.data.Repository;
using payroll_api.email;

namespace payroll_api.Infrastructure.Service
{

    public class UserService : IUserService
    {
        private readonly PayrollContext _context;
        private readonly IMapper _mapper;
        private readonly ISettingService _settingService;
        private readonly IIncomeService _incomeService;
        private readonly IOutcomeService _outcomeService;
        private readonly IIdentityService _identityService;
        private readonly DapperContext _dapperContext;
        private readonly IUserRepository _userRepository;
        private readonly EmailVerificationLinkGenerator _emailVerificationLinkGenerator;
        private readonly IEmailSender _emailSender;

        public UserService(
            PayrollContext context,
            IMapper mapper,
            ISettingService settingService,
            IIncomeService incomeService,
            IOutcomeService outcomeService,
            IIdentityService identityService,
            DapperContext dapperContext,
            IUserRepository userRepository,
            EmailVerificationLinkGenerator emailVerificationLinkGenerator,
            IEmailSender emailSender
        )
        {
            _context = context;
            _mapper = mapper;
            _settingService = settingService ?? throw new ArgumentNullException(nameof(settingService));
            _incomeService = incomeService ?? throw new ArgumentNullException(nameof(incomeService));
            _outcomeService = outcomeService ?? throw new ArgumentNullException(nameof(outcomeService));
            _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
            _dapperContext = dapperContext ?? throw new ArgumentNullException(nameof(dapperContext));
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _emailVerificationLinkGenerator = emailVerificationLinkGenerator ?? throw new ArgumentNullException(nameof(emailVerificationLinkGenerator));
            _emailSender = emailSender ?? throw new ArgumentNullException(nameof(emailSender));
        }

        public async Task<UserViewModel> GetById(int id)
        {
            return await _userRepository.GetUser(id);
        }

        public async Task Register(RegisterUserViewModel registerUserViewModel)
        {
            // Validate data
            var isExistEmail = await _context.Users.AnyAsync(x => x.Email == registerUserViewModel.Email);
            if (isExistEmail) throw new AppException("อีเมลล์นี้ถูกใช้งานเเล้ว");

            var user = _mapper.Map<User>(registerUserViewModel);
            user.UserType = (int)UserType.Manager;
            user.StartDate = DateTime.Now;
            user.ExpireDate = DateTime.Now.AddDays(7);
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(registerUserViewModel.Password);

            // Create email verification token
            var emailVerificationToken = CreaetEmailVerificationToken();
            user.EmailVerificationTokens.Add(emailVerificationToken);

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            var verificationLink = _emailVerificationLinkGenerator
                .Create(
                    registerUserViewModel.VerifyEmailURL,
                    emailVerificationToken.Token,
                    user.Email
                );

            await SendVerificationEmail(user.Email, verificationLink);
        }

        private static EmailVerificationToken CreaetEmailVerificationToken()
        {
            var utcNow = DateTime.UtcNow;
            var emailVerificationToken = new EmailVerificationToken
            {
                Token = Guid.NewGuid(),
                CreatedAtUtc = utcNow,
                ExpiresAtUtc = utcNow.AddDays(1),
            };
            return emailVerificationToken;
        }

        public async Task VerifyEmail(EmailVerificationViewModel emailVerificationViewModel)
        {
            var isVerifiedUser = await _userRepository.IsVerifiedUser(emailVerificationViewModel.Email);
            if (isVerifiedUser) return;

            var emailVerificationToken = await _context.EmailVerificationTokens
                .Include(x => x.User)
                .FirstOrDefaultAsync(x => x.Token == emailVerificationViewModel.Token);

            var utcNow = DateTime.UtcNow;
            if (emailVerificationToken is null || emailVerificationToken.ExpiresAtUtc < utcNow)
                throw new AppException("ลิงค์หมดอายุ");

            if (emailVerificationToken.User.IsEmailVerified) return;

            emailVerificationToken.User.IsEmailVerified = true;
            _context.EmailVerificationTokens.Remove(emailVerificationToken);
            await _context.SaveChangesAsync();

            await _incomeService.GenerateSystemIncomes(emailVerificationToken.User.Id);
            await _outcomeService.GenerateSystemOutcomes(emailVerificationToken.User.Id);
            await _settingService.GenerateSystemSetting(emailVerificationToken.User.Id);
        }

        public async Task ResendEmailVerification(ResendEmailVerificationViewModel resendEmailVerificationViewModel)
        {
            var emailVerificationToken = await _context.EmailVerificationTokens
                .Include(x => x.User)
                .FirstOrDefaultAsync(x => x.Token == resendEmailVerificationViewModel.Token);

            int userId;
            if (emailVerificationToken is null)
                userId = await _userRepository.GetUserIdByEmail(resendEmailVerificationViewModel.Email);
            else
                userId = emailVerificationToken.UserId;

            var newEmailVerificationToken = CreaetEmailVerificationToken();
            newEmailVerificationToken.UserId = userId;

            var verificationLink = _emailVerificationLinkGenerator
                .Create(
                    resendEmailVerificationViewModel.VerifyEmailURL,
                    newEmailVerificationToken.Token,
                    resendEmailVerificationViewModel.Email
                );

            if (emailVerificationToken is not null)
                _context.EmailVerificationTokens.Remove(emailVerificationToken);
            _context.EmailVerificationTokens.Add(newEmailVerificationToken);
            await _context.SaveChangesAsync();
            await SendVerificationEmail(resendEmailVerificationViewModel.Email, verificationLink);
        }

        private async Task SendVerificationEmail(string email, string verificationLink)
        {
            var message = new Message(
                [email],
                "โปรดทำการยืนยันตัวตน เพื่อเข้าใช้งานระบบ",
                $"กรุณาคลิ๊กลิ้งนี้เพื่อยืนยันอีเมลของคุณ: <a href='{verificationLink}'>ยืนยัน</a>"
            );
            await _emailSender.SendEmailAsync(message);
        }

        public async Task<PageResponseViewModel<List<UserDetailViewModel>>> GetUsers(PageFilterViewModel filter)
        {
            using var conn = _dapperContext.CreateConnection();

            var baseSql = @"
                SELECT 
                    Id,
                    FirstName,
                    LastName,
                    FirstName + ' ' + LastName AS FullName,
                    Email,
                    PackageType,
                    CASE PackageType 
                    WHEN 1 THEN 'Platinum'
                    WHEN 2 THEN 'Premium'
                    WHEN 3 THEN 'VIP'
                    ELSE 'Free' END AS PackageTypeName,
                    ExpireDate
                FROM Users WITH (NOLOCK)
            ";
            var sqlFilter = "";

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                filter.SearchTerm += "%";
                sqlFilter = " WHERE Email LIKE @SearchTerm OR FirstName + ' ' + LastName LIKE @SearchTerm";
            }

            sqlFilter += filter.GetSortQuery("ORDER BY Id");

            var sqlCount = "SELECT COUNT(1) FROM Users";
            var total = await conn.ExecuteScalarAsync<int>(sqlCount);

            var sql = baseSql + sqlFilter + " OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
            var pageData = await conn.QueryAsync<UserDetailViewModel>(sql, new
            {
                SearchTerm = filter.SearchTerm,
                Offset = filter.PageIndex * filter.PageSize,
                PageSize = filter.PageSize
            });

            var result = PageHelper.CreatePagedReponse(
                pageData.ToList(),
                filter,
                total
            );

            return result;
        }

        public async Task DeleteUser(int id)
        {
            var isExistUser = await _context.Users.AnyAsync(x => x.Id == id);
            if (!isExistUser) throw new AppException("ไม่พบผู้ใช้");

            await _context.Database.ExecuteSqlRawAsync("EXEC DeleteUserData @p0", id);
        }

        public async Task UpdateUser(int id, UpdateUserViewModel userDetailViewModel)
        {
            var isExistEmail = await _context.Users.AnyAsync(x => x.Email == userDetailViewModel.Email && x.Id != userDetailViewModel.ID);
            if (isExistEmail) throw new AppException("พบอีเมลซ้ำในระบบ");

            var user = await _context.Users
                .FindAsync(id) ?? throw new AppException("ไม่พบลูกค้า");
            _mapper.Map(userDetailViewModel, user);

            _context.Users.Update(user);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateUserProfile(UserProfileViewModel userProfileViewModel)
        {
            var id = _identityService.GetUserId();

            var isExistEmail = await _context.Users.AnyAsync(x => x.Email == userProfileViewModel.Email && x.Id != id);
            if (isExistEmail) throw new AppException("พบอีเมลซ้ำในระบบ");

            var user = await _context.Users
                .FindAsync(id) ?? throw new AppException("ไม่พบลูกค้า");
            _mapper.Map(userProfileViewModel, user);

            _context.Users.Update(user);
            await _context.SaveChangesAsync();
        }
    }
}
