using payroll_api.utility.ViewModel;
using payroll_api.utility.Filter;

namespace payroll_api.Infrastructure.Service
{
    public interface IUserService
    {
        Task DeleteUser(int id);
        Task<UserViewModel> GetById(int id);
        Task<PageResponseViewModel<List<UserDetailViewModel>>> GetUsers(PageFilterViewModel filter);
        Task Register(RegisterUserViewModel registerUserViewModel);
        Task ResendEmailVerification(ResendEmailVerificationViewModel resendEmailVerificationViewModel);
        Task UpdateUser(int id, UpdateUserViewModel userDetailViewModel);
        Task UpdateUserProfile(UserProfileViewModel userProfileViewModel);
        Task VerifyEmail(EmailVerificationViewModel emailVerificationViewModel);
    }
}
