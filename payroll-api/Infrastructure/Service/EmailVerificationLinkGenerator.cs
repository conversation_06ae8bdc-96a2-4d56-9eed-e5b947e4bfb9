using payroll_api.domain;

namespace payroll_api.Infrastructure.Service
{
    public sealed class EmailVerificationLinkGenerator
    {
        public string Create(string endpoint, Guid token, string email)
        {
            if (string.IsNullOrEmpty(endpoint) || token == Guid.Empty)
                throw new System.Exception("Failed to generate verification link");

            return $"{endpoint}?token={token}&email={email}";
        }
    }
}
