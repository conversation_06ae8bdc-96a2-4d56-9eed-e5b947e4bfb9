﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using payroll_api.data;
using payroll_api.domain;
using payroll_api.Infrastructure.Authorization;
using payroll_api.Infrastructure.Exception;
using payroll_api.Infrastructure.Helper;
using payroll_api.utility.Enum;
using payroll_api.utility.ViewModel;

namespace payroll_api.Infrastructure.Service
{
    public interface IAuthenticationService
    {
        AuthenticateResponseViewModel Authenticate(AuthenticateViewModel authenticateViewModel, string ipAddress);
        AuthenticateResponseViewModel RefreshToken(string token, string ipAddress);
        void RevokeToken(string token, string ipAddress);
    }
    public class AuthenticationService : IAuthenticationService
    {
        private readonly PayrollContext _context;
        private readonly IJwtUtils _jwtUtils;
        private readonly AppSettings _appSettings;

        public AuthenticationService(
            PayrollContext context,
            IJwtUtils jwtUtils,
            IOptions<AppSettings> appSettings
        )
        {
            _context = context;
            _jwtUtils = jwtUtils;
            _appSettings = appSettings.Value;
        }

        public AuthenticateResponseViewModel Authenticate(AuthenticateViewModel authenticateViewModel, string ipAddress)
        {
            var user = _context.Users
                .Include(u => u.RefreshTokens)
                .SingleOrDefault(u => u.Email == authenticateViewModel.Username);

            // validate
            if (user is null || !BCrypt.Net.BCrypt.Verify(
                authenticateViewModel.Password, user.PasswordHash))
                throw new AppException("บัญชี / รหัสผ่าน ไม่ถูกต้อง โปรดลองอีกครั้ง");

            if (user.ExpireDate < DateTime.Now) throw new AppException("บัญชีหมดอายุ โปรดต่ออายุเเพ็คเก็จหรือติดต่อผู้ดูแลระบบ");
            if (!user.IsEmailVerified) throw new AppException("บัญชียังไม่ถูกยืนยัน โปรดทำการยืนยันบัญชีจากอีเมลที่ได้รับหรือติดต่อผู้ดูแลระบบ");

            // authentication successful so generate jwt and refresh token
            var jwtToken = _jwtUtils.GenerateJwtToken(user);
            var refreshToken = _jwtUtils.GenerateRefreshToken(ipAddress);
            user.RefreshTokens.Add(refreshToken);

            // remove old refresh tokens from user
            RemoveOldRefreshTokens(user);

            // save changes to db
            _context.Update(user);
            _context.SaveChanges();

            return new AuthenticateResponseViewModel(
                user.Id,
                user.FirstName,
                user.LastName,
                user.Email,
                (UserType)user.UserType,
                user.PackageType,
                user.ExpireDate,
                jwtToken,
                refreshToken.Token
            );
        }

        public AuthenticateResponseViewModel RefreshToken(string token, string ipAddress)
        {
            var user = GetUserByRefreshToken(token);
            var refreshToken = user.RefreshTokens
                .Single(u => u.Token == token);

            if (refreshToken.IsRevoked)
            {
                // revoke all descendant tokens in case this token has been compromised
                RevokeDescendantRefreshTokens(refreshToken, user, ipAddress, $"Attempted reuse of revoked ancestor token: {token}");
                _context.Update(user);
                _context.SaveChanges();
            }

            if (!refreshToken.IsActive)
                throw new AppException("Invalid token");

            // replace old refresh token with a new one
            var newRefreshToken = rotateRefreshToken(refreshToken, ipAddress);
            user.RefreshTokens.Add(newRefreshToken);

            // remove old refresh tokens from user
            RemoveOldRefreshTokens(user);

            // save changes to db
            _context.Update(user);
            _context.SaveChanges();

            // generate new jwt
            var jwtToken = _jwtUtils.GenerateJwtToken(user);

            return new AuthenticateResponseViewModel(
                user.Id,
                user.FirstName,
                user.LastName,
                user.Email,
                (UserType)user.UserType,
                user.PackageType,
                user.ExpireDate,
                jwtToken,
                newRefreshToken.Token
            );
        }

        public void RevokeToken(string token, string ipAddress)
        {
            var user = GetUserByRefreshToken(token);
            var refreshToken = user.RefreshTokens
                .Single(u => u.Token == token);

            if (!refreshToken.IsActive)
                throw new AppException("Invalid token");

            // revoke token and save
            RevokeRefreshToken(refreshToken, ipAddress, "Revoked without replacement");
            _context.Update(user);
            _context.SaveChanges();
        }

        #region Helper Methods

        private RefreshToken rotateRefreshToken(RefreshToken refreshToken, string ipAddress)
        {
            var newRefreshToken = _jwtUtils.GenerateRefreshToken(ipAddress);
            RevokeRefreshToken(refreshToken, ipAddress, "Replaced by new token", newRefreshToken.Token);
            return newRefreshToken;
        }

        private void RevokeDescendantRefreshTokens(RefreshToken refreshToken, User user, string ipAddress, string reason)
        {
            // recursively traverse the refresh token chain and ensure all descendants are revoked
            if (!string.IsNullOrEmpty(refreshToken.ReplacedByToken))
            {
                var childToken = user.RefreshTokens
                    .SingleOrDefault(u => u.Token == refreshToken.ReplacedByToken);
                if (childToken?.IsActive == true)
                    RevokeRefreshToken(childToken, ipAddress, reason);
                else
                    RevokeDescendantRefreshTokens(childToken, user, ipAddress, reason);
            }
        }

        private void RevokeRefreshToken(RefreshToken token, string ipAddress, string reason = null, string replacedByToken = null)
        {
            token.Revoked = DateTime.UtcNow;
            token.RevokedByIp = ipAddress;
            token.ReasonRevoked = reason;
            token.ReplacedByToken = replacedByToken;
        }

        private User GetUserByRefreshToken(string token)
        {
            var user = _context.Users
                .Include(u => u.RefreshTokens)
                .Where(u => u.ExpireDate > DateTime.Now)
                .SingleOrDefault(u => u.RefreshTokens.Any(t => t.Token == token)) ?? throw new AppException("Invalid token");
            return user;
        }

        private void RemoveOldRefreshTokens(User user)
        {
            user.RefreshTokens.RemoveAll(
                u => !u.IsActive &&
                u.Created.AddDays(_appSettings.RefreshTokenTTL) <= DateTime.UtcNow
            );
        }
        #endregion
    }
}
