﻿using System.Data.SqlClient;
using System.Net;
using System.Text.Json;
using payroll_api.utility.ViewModel;

namespace payroll_api.Infrastructure.Exception
{
    public class ErrorHandlerMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ErrorHandlerMiddleware> _logger;

        public ErrorHandlerMiddleware(RequestDelegate next, ILogger<ErrorHandlerMiddleware> logger)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (SqlException error)
            {
                _logger.LogError(error, "SqlException occurred: {Message}", error.Message);

                var response = context.Response;
                response.ContentType = "application/json";

                var result = new BaseResponseViewModel();
                result.Success = false;
                result.Errors.Add(error.Message);

                response.StatusCode = (int)HttpStatusCode.InternalServerError;
                result.StatusCode = HttpStatusCode.InternalServerError;

                await response.WriteAsync(JsonSerializer.Serialize(result));
            }
            catch (System.Exception error)
            {
                _logger.LogError(error, "Exception occurred: {Message}", error.Message);

                var response = context.Response;
                response.ContentType = "application/json";

                var result = new BaseResponseViewModel();
                result.Success = false;
                result.Errors.Add(error.Message);

                switch (error)
                {
                    case AppException e:
                        // custom application error
                        response.StatusCode = (int)HttpStatusCode.BadRequest;
                        result.StatusCode = HttpStatusCode.BadRequest;
                        break;
                    case KeyNotFoundException e:
                        // not found error
                        response.StatusCode = (int)HttpStatusCode.NotFound;
                        result.StatusCode = HttpStatusCode.NotFound;
                        break;
                    default:
                        // unhandled error
                        response.StatusCode = (int)HttpStatusCode.InternalServerError;
                        result.StatusCode = HttpStatusCode.InternalServerError;
                        break;
                }

                await response.WriteAsync(JsonSerializer.Serialize(result));
            }
        }
    }
}
