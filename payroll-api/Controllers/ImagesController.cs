﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.Infrastructure.Service;
using payroll_api.service;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers;
[Authorize]
[Route("api/[controller]")]
[ApiController]

public class ImagesController(IIdentityService identityService, ISettingService settingService) : ControllerBase
{
    private readonly IIdentityService _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
    private readonly ISettingService _settingService = settingService ?? throw new ArgumentNullException(nameof(settingService));

    [HttpPost("UploadImage/{name}", Name = "UploadImage")]
    public async Task<IActionResult> UploadImage(IFormFile formFile, [FromRoute] string name)
    {
        if (formFile is null || formFile.Length == 0) return BadRequest("Invalid file");
        if (formFile.Length > 2097152) return BadRequest("File size exceeds the 2 MB limit");

        var userId = _identityService.GetUserId();
        var settingImageViewModel = new SettingImageViewModel
        {
            UserId = userId,
            Content = await ReadFileDataAsync(formFile),
            Name = name
        };
        await _settingService.UploadImage(settingImageViewModel);

        return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
    }

    [HttpDelete("RemoveImage/{id}", Name = "RemoveImage")]
    public async Task<IActionResult> RemoveImage([FromRoute] int id)
    {
        await _settingService.RemoveImage(id);
        return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
    }

    private async Task<byte[]> ReadFileDataAsync(IFormFile file)
    {
        using (var memoryStream = new MemoryStream())
        {
            await file.CopyToAsync(memoryStream);
            return memoryStream.ToArray();
        }
    }
}
