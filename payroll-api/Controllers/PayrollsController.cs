using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.service;
using payroll_api.utility.Helper;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PayrollsController(
        IPayrollService payrollService
        ) : ControllerBase
    {
        private readonly IPayrollService _payrollService = payrollService ?? throw new ArgumentNullException(nameof(payrollService));

        [HttpGet("DownloadImportPayrollExcel")]
        public IActionResult DownloadImportPayrollExcel()
        {
            var result = _payrollService.DownloadImportPayrollExcel();
            return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "ImportPayrollExcel.xlsx");
        }

        [HttpGet("DownloadPayrollExcel/{payrollId}")]
        public async Task<IActionResult> DownloadPayrollExcelAsync([FromRoute] int payrollId)
        {
            var result = await _payrollService.DownloadPayrollExcelReport(payrollId);
            return File(result, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "PayrollExcel.xlsx");
        }


        [HttpPost("CreatePayroll")]
        public async Task<IActionResult> CreatePayroll([FromBody] CreatePayrollViewModel createPayrollViewModel)
        {
            var payrollId = await _payrollService.CreatePayroll(createPayrollViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, payrollId));
        }

        [HttpGet("GetPayroll/{encryptId}")]
        public async Task<IActionResult> GetPayroll([FromRoute] string encryptId, [FromQuery] bool copy = false)
        {
            var id = AesEncryption.Decryption(encryptId);
            var result = await _payrollService.GetPayroll(Convert.ToInt32(id), copy);
            if (result is not null)
                return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
            else
                return Ok(new BaseResponseViewModel(false, HttpStatusCode.NotFound));
        }

        [HttpGet("GetPayroll")]
        public async Task<IActionResult> GetPayrolls()
        {
            var result = await _payrollService.GetPayrolls();
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPatch("CancelPayroll/{encryptId}")]
        public async Task<IActionResult> CancelPayroll([FromRoute] string encryptId)
        {
            var id = Convert.ToInt32(AesEncryption.Decryption(encryptId));
            await _payrollService.CancelPayroll(id);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpGet("History/{encryptId}")]
        public async Task<IActionResult> GetPayrollHistory(string encryptId)
        {
            var id = Convert.ToInt32(AesEncryption.Decryption(encryptId));
            var result = await _payrollService.GetPayrollHistory(id);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPost("SendEmailSlip")]
        public async Task<IActionResult> SendEmailSlip([FromBody] SendEmailSlipViewModel sendEmailSlipViewModel)
        {
            await _payrollService.SendEmailSlip(sendEmailSlipViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPatch("Pay/{encryptId}")]
        public async Task<IActionResult> PayPayroll([FromRoute] string encryptId, [FromBody] PayPayrollViewModel payPayrollViewModel)
        {
            var id = Convert.ToInt32(AesEncryption.Decryption(encryptId));
            await _payrollService.PayPayroll(id, payPayrollViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPatch("Approve/{encryptId}")]
        public async Task<IActionResult> Approve([FromRoute] string encryptId)
        {
            var id = Convert.ToInt32(AesEncryption.Decryption(encryptId));
            await _payrollService.Approve(id);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpGet("GenerateCode")]
        public async Task<IActionResult> GenerateCode()
        {
            var result = await _payrollService.GenerateCode();
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }
    }
}
