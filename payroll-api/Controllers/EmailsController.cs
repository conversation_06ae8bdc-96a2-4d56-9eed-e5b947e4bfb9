﻿using Microsoft.AspNetCore.Mvc;
using payroll_api.email;

namespace payroll_api.Controllers;

[Route("api/[controller]")]
[ApiController]
public class EmailsController : ControllerBase
{
    private readonly IEmailSender _emailSender;

    public EmailsController(IEmailSender emailSender)
    {
        _emailSender = emailSender ?? throw new ArgumentNullException(nameof(emailSender));
    }

    [HttpGet("SendTestEmail")]
    public async Task<IActionResult> SendTestEmail()
    {
        var message = new Message([
            "<EMAIL>" ],
            "Test email async",
            "This is the content from our async email."
        );
        await _emailSender.SendEmailAsync(message);

        return Ok();
    }
}

