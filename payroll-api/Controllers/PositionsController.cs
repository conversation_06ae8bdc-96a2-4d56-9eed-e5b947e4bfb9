﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.service;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PositionsController(
        IPositionService positionService,
        IDepartmentService departmentService
        ) : ControllerBase
    {
        private readonly IPositionService _positionService = positionService ?? throw new ArgumentNullException(nameof(positionService));
        private readonly IDepartmentService _departmentService = departmentService ?? throw new ArgumentNullException(nameof(departmentService));

        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] int? departmentId)
        {
            var result = await _positionService.GetPositions(departmentId);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("page")]
        public async Task<IActionResult> GetPositionPage([FromQuery] PositionPageFilterViewModel pageFilterViewModel)
        {
            var validFilter = new PositionPageFilterViewModel(pageFilterViewModel.PageIndex, pageFilterViewModel.PageSize, pageFilterViewModel.DepartmentId);
            var result = await _positionService.GetPositionPage(validFilter);
            result.Message = await _departmentService.GetDepartmentName(pageFilterViewModel.DepartmentId);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPost(Name = "Create Position")]
        public async Task<IActionResult> Create([FromBody] PositionViewModel positionViewModel)
        {
            await _positionService.CreatePosition(positionViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody] PositionViewModel positionViewModel)
        {
            await _positionService.UpdatePosition(positionViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }
    }
}

