﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.service;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class DepartmentsController(
        IDepartmentService departmentService,
        ILogger<DepartmentsController> logger
        ) : ControllerBase
    {
        private readonly IDepartmentService _departmentService = departmentService ?? throw new ArgumentNullException(nameof(departmentService));
        private readonly ILogger<DepartmentsController> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        [HttpGet(Name = "GetDepartments")]
        public async Task<IActionResult> Get()
        {
            var result = await _departmentService.GetDepartments();
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("page")]
        public async Task<IActionResult> GetDepartmentPage([FromQuery] PageFilterViewModel pageFilterViewModel)
        {
            var validFilter = new PageFilterViewModel(pageFilterViewModel.PageIndex, pageFilterViewModel.PageSize);
            var result = await _departmentService.GetDepartmentPage(validFilter);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("{ID}", Name = "GetDepartmentDetail")]
        public async Task<IActionResult> Get([FromRoute] int ID)
        {
            var result = await _departmentService.GetDepartment(ID);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPost(Name = "CreateDepartment")]
        public async Task<IActionResult> Create([FromBody] DepartmentViewModel departmentViewModel)
        {
            await _departmentService.CreateDepartment(departmentViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("{ID}", Name = "UpdateDepartment")]
        public async Task<IActionResult> Update([FromRoute] int ID, [FromBody] DepartmentViewModel departmentViewModel)
        {
            await _departmentService.UpdateDepartment(ID, departmentViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("Active/{id}")]
        public async Task<IActionResult> UpdateActiveDepartment([FromRoute] int id, [FromBody] UpdateDepartmentActiveViewModel updateDepartmentActiveViewModel)
        {
            await _departmentService.UpdateActiveDepartment(id, updateDepartmentActiveViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpDelete("{ID}", Name = "DeleteDepartment")]
        public async Task<IActionResult> Delete([FromRoute] int ID)
        {
            await _departmentService.DeleteDepartment(ID);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }
    }
}
