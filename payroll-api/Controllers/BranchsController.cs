using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.service;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class BranchsController(IBranchService branchService) : ControllerBase
    {
        private readonly IBranchService _branchService = branchService ?? throw new ArgumentNullException(nameof(branchService));

        [HttpGet]
        public async Task<IActionResult> GetBranchPage()
        {
            var result = await _branchService.GetBranches();
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("page")]
        public async Task<IActionResult> GetBranchPage([FromQuery] PageFilterViewModel pageFilterViewModel)
        {
            var branchFilter = new PageFilterViewModel(
                pageFilterViewModel.PageIndex,
                pageFilterViewModel.PageSize,
                pageFilterViewModel.SortBy
            );
            var result = await _branchService.GetBranchPage(branchFilter);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBranch(int id)
        {
            var result = await _branchService.GetBranch(id);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPost]
        public async Task<IActionResult> CreateBranch([FromBody] BranchDetailViewModel branchViewModel)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            await _branchService.CreateBranch(branchViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBranch([FromRoute] int id, [FromBody] UpdateBranchViewModel updateBranchViewModel)
        {
            await _branchService.UpdateBranch(id, updateBranchViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("Active/{id}")]
        public async Task<IActionResult> UpdateActiveBranch([FromRoute] int id, [FromBody] UpdateBranchActiveViewModel updateBranchViewModel)
        {
            await _branchService.UpdateActiveBranch(id, updateBranchViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }


        [HttpDelete("{branchId}")]
        public async Task<IActionResult> DeleteBranch(int branchId, [FromQuery] bool deleteEmployee = false)
        {
            await _branchService.DeleteBranch(branchId, deleteEmployee);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }
    }
}
