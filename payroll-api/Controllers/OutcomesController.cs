﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.service;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OutcomesController(
        IOutcomeService outcomeService
        ) : ControllerBase
    {
        private readonly IOutcomeService _outcomeService = outcomeService ?? throw new ArgumentNullException(nameof(outcomeService));

        [HttpGet]
        public async Task<IActionResult> GetOutcomes([FromQuery] bool? active)
        {
            var result = await _outcomeService.GetOutcomes(active);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("page")]
        public async Task<IActionResult> GetIncomePage([FromQuery] PageFilterViewModel pageFilterViewModel)
        {
            var validFilter = new PageFilterViewModel(pageFilterViewModel.PageIndex, pageFilterViewModel.PageSize);
            var result = await _outcomeService.GetOutcomePage(validFilter);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPost]
        public async Task<IActionResult> CreateOutcomes([FromBody] OutcomeViewModel outcomeViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            await _outcomeService.CreateOutcome(outcomeViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateOutcomes([FromRoute] int id, [FromBody] UpdateOutcomeViewModel outcomeViewModel)
        {
            await _outcomeService.UpdateOutcome(id, outcomeViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("Active/{id}")]
        public async Task<IActionResult> UpdateActiveBranch([FromRoute] int id, [FromBody] UpdateOutcomeActiveViewModel updateOutcomeActiveViewModel)
        {
            await _outcomeService.UpdateActiveOutcome(id, updateOutcomeActiveViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }
    }
}
