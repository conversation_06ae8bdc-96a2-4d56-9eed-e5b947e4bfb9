﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.Infrastructure.Exception;
using payroll_api.Infrastructure.Service;
using payroll_api.report.Reports;
using payroll_api.service;
using payroll_api.utility.Constant;
using payroll_api.utility.Enum;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;
using QuestPDF.Fluent;
using System.Data;
using System.Data.SqlClient;
using System.Net;
using System.Text;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class EmployeeController(
        IEmployeeService employeeService,
        IDepartmentService departmentService,
        IWebHostEnvironment webHostEnvironment,
        IConfiguration configuration,
        IIdentityService identityService,
        ISettingService settingService,
        IBranchService branchService
        ) : ControllerBase
    {
        private readonly IEmployeeService _employeeService = employeeService ?? throw new ArgumentNullException(nameof(employeeService));
        private readonly IDepartmentService _departmentService = departmentService ?? throw new ArgumentNullException(nameof(departmentService));
        private readonly IWebHostEnvironment _webHostEnvironment = webHostEnvironment ?? throw new ArgumentNullException(nameof(webHostEnvironment));
        private readonly IConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        private readonly IIdentityService _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));
        private readonly ISettingService _settingService = settingService ?? throw new ArgumentNullException(nameof(settingService));
        private readonly IBranchService _branchService = branchService ?? throw new ArgumentNullException(nameof(branchService));

        [HttpGet(Name = "GetEmployee")]
        public async Task<IActionResult> Get()
        {
            var result = await _employeeService.GetEmployees();
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("page")]
        public async Task<IActionResult> GetEmployeePage([FromQuery] EmployeeFilterViewModel pageFilterViewModel)
        {
            var filter = new EmployeeFilterViewModel(
                pageFilterViewModel.PageIndex,
                pageFilterViewModel.PageSize,
                pageFilterViewModel.SearchTerm,
                pageFilterViewModel.DepartmentId,
                pageFilterViewModel.SortBy,
                pageFilterViewModel.BranchId,
                pageFilterViewModel.PositionId,
                pageFilterViewModel.EmployeeType,
                pageFilterViewModel.MinSalary,
                pageFilterViewModel.MaxSalary,
                pageFilterViewModel.IsActive
            );
            var result = await _employeeService.GetEmployeePage(filter);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("{id}", Name = "GetEmployeeDetail")]
        public async Task<IActionResult> GetEmployeeDetail([FromRoute] int id)
        {
            var result = await _employeeService.GetEmployeeDetail(id);
            return result is not null
                ? Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result))
                : Ok(new BaseResponseViewModel(false, HttpStatusCode.NotFound));
        }

        [HttpGet("Details/{id}", Name = "GetEmployeeFullDetail")]
        public async Task<IActionResult> GetEmployeeFullDetail([FromRoute] int id)
        {
            var result = await _employeeService.GetEmployeeFullDetail(id);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("{id}/SalaryHistory", Name = "GetEmployeeSalaryHistory")]
        public async Task<IActionResult> GetEmployeeSalaryHistory([FromRoute] int id, [FromQuery] EmployeeSalaryHistoryFilterViewModel pageFilterViewModel)
        {
            var filter = new EmployeeSalaryHistoryFilterViewModel(
                pageFilterViewModel.PageIndex,
                pageFilterViewModel.PageSize,
                employeeId: id,
                sortBy: pageFilterViewModel.SortBy
            );
            var result = await _employeeService.GetEmployeeSalaryHistory(filter);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPut("{id}", Name = "UpdateEmployee")]
        public async Task<IActionResult> UpdateEmployee([FromRoute] int id, [FromBody] UpdateEmployeeViewModel updateEmployeeViewModel)
        {
            await _employeeService.UpdateEmployee(id, updateEmployeeViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmployee([FromRoute] int id)
        {
            await _employeeService.DeleteEmployee(id);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPost(Name = "CreateEmployee")]
        public async Task<IActionResult> Create([FromBody] CreateEmployeeViewModel employeeViewModel)
        {
            await _employeeService.CreateEmployee(employeeViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpGet("DownloadCreateEmployeeExcel", Name = "GetCreateEmployeeExcel")]
        public async Task<IActionResult> DownloadCreateEmployeeExcel()
        {
            var departments = await _departmentService.GetActiveDepartments();

            var empExcelColumns = new List<EmployeeExcelColumn> {
                new() { Width = 15, Name = "ชื่อ", Type = "string", ExampleValue = "สมชาย" },
                new() { Width = 15, Name = "นามสกุล", Type = "string", ExampleValue = "ขยันดี" },
                new() { Width = 15, Name = "เบอร์โทร", Type = "string", ExampleValue = "0987654321" },
                new() { Width = 15, Name = "อีเมล์", Type = "string", ExampleValue = "<EMAIL>" },
                new() { Width = 15, Name = "แผนก", Type = "int", ExampleValue = departments?.FirstOrDefault()?.Code ?? string.Empty },
                new() { Width = 15, Name = "เงินเดือน", Type = "decimal", ExampleValue = "15000" },
                new() { Width = 15, Name = "วันที่เริ่มงาน", Type = "DateTime", ExampleValue = "19/8/2023" },
                new() { Width = 15, Name = "ประเภท", Type = "int", ExampleValue = "1" },
            };

            var userId = _identityService.GetUserId();
            var branchs = await _branchService.GetBranches();
            if (branchs?.Any() == true)
                empExcelColumns.Add(new() { Width = 15, Name = "สาขา", Type = "int", ExampleValue = branchs.FirstOrDefault()?.Code ?? string.Empty });

            var dataTable = new DataTable();
            empExcelColumns.ForEach(x => dataTable.Columns.Add(x.Name, Type.GetType(x.Type) ?? typeof(string)));

            // Create a new workbook
            using var workbook = new XLWorkbook();
            // Add a worksheet to the workbook
            var worksheet = workbook.Worksheets.Add("Sheet1");

            // Load the data from the DataTable to the worksheet
            worksheet.Cell(1, 1).InsertTable(dataTable);
            var htFirstCell = worksheet.Cell(1, 1);
            var htLastCell = worksheet.Cell(1, empExcelColumns.Count);
            var headersRange = worksheet.Range(htFirstCell, htLastCell);
            headersRange.Style.Font.SetFontColor(XLColor.Onyx);
            headersRange.Style.Fill.SetBackgroundColor(XLColor.FromHtml(ExcelSettingKeys.PrimaryColor));
            worksheet.Tables?.FirstOrDefault()?.SetShowAutoFilter(false);

            // Set the column width for specific columns
            for (int i = 0; i < empExcelColumns.Count; i++)
            {
                worksheet.Column(i + 1).Width = empExcelColumns[i].Width;
            }

            #region "Worksheet2"
            var worksheet2 = workbook.Worksheets.Add("ตัวอย่างข้อมูล");

            worksheet2.Cell(1, 1).Value = "ตัวอย่างข้อมูล";
            worksheet2.Range(1, 1, 1, 2).Merge().AddToNamed("Titles");
            for (int i = 0; i < empExcelColumns.Count; i++)
            {
                worksheet2.Cell(2, i + 1).Value = empExcelColumns[i].Name;
                worksheet2.Cell(2, i + 1).Style.Fill.SetBackgroundColor(XLColor.LightGray);
                worksheet2.Cell(3, i + 1).Value = empExcelColumns[i].ExampleValue;
            }

            worksheet2.Cell(5, 1).Value = "แผนก";
            worksheet2.Range(5, 1, 5, 2).Merge().AddToNamed("Titles");
            worksheet2.Cell(6, 1).InsertData(departments.Select(x => new { x.Name, x.Code }));
            worksheet2.Cell(5, 4).Value = "ประเภท";
            worksheet2.Range(5, 4, 5, 5).Merge().AddToNamed("Titles");
            var employeeTypes = new List<dynamic> {
                new { Name = "ไม่ระบุ", Id = (int)EmployeeType.None },
                new { Name = EmployeeType.FullTime, Id = (int)EmployeeType.FullTime },
                new { Name = EmployeeType.PartTime, Id = (int)EmployeeType.PartTime }
            };
            worksheet2.Cell(6, 4).InsertData(employeeTypes);
            worksheet2.Cell(5, 7).Value = "สาขา";
            worksheet2.Range(5, 7, 5, 8).Merge().AddToNamed("Titles");
            worksheet2.Cell(6, 7).InsertData(branchs.Select(x => new { x.Name, x.Code }));
            #endregion

            // Prepare the style for the titles
            var titlesStyle = workbook.Style;
            titlesStyle.Font.Bold = true;
            titlesStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            titlesStyle.Fill.BackgroundColor = XLColor.FromHtml(ExcelSettingKeys.PrimaryColor);

            // Format all titles in one shot
            workbook.NamedRanges.NamedRange("Titles").Ranges.Style = titlesStyle;

            // Save the workbook to a file
            using var ms = new MemoryStream();
            workbook.SaveAs(ms);
            return File(ms.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "CreateEmployeeExcel.xlsx");
        }

        [HttpGet("Report")]
        public async Task<IActionResult> GetEmployeeReportAsync([FromQuery] EmployeeFilterViewModel employeeFilterViewModel)
        {
            try
            {
                var userId = _identityService.GetUserId();
                var model = await _employeeService.GetEmployeeReport(userId, employeeFilterViewModel);
                if (model is null || model.Departments?.Count == 0) return NotFound();

                model.CompanyLogo = _settingService.GetOptionImageByName<byte[]>(userId, SettingImageKey.CompanyLogo);
                model.CompanyInfo = await _settingService.GetOptionCompanyInfo(userId);
                var document = new EmployeeSummaryDocument(model);
                var pdfBytes = document.GeneratePdf();

                return File(pdfBytes, "application/pdf", $"{nameof(EmployeeSummaryDocument)}.pdf");
            }
            catch (Exception ex)
            {
                throw new AppException($"{nameof(GetEmployeeReportAsync)}: ${ex.Message}");
            }
        }

        [HttpPost("ImportEmployeeExcel")]
        public async Task<IActionResult> ImportEmployeeExcel(IFormFile formFile)
        {
            // Create a Folder
            var path = Path.Combine(_webHostEnvironment.ContentRootPath, "Upload");
            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);

            // Create a new DataTable with columns matching your Excel data
            DataTable dataTable = new();
            dataTable.Columns.Add("FirstName", typeof(string));
            dataTable.Columns.Add("LastName", typeof(string));
            dataTable.Columns.Add("Email", typeof(string));
            dataTable.Columns.Add("Phone", typeof(string));
            dataTable.Columns.Add("Active", typeof(bool));
            dataTable.Columns.Add("DepartmentId", typeof(int));
            dataTable.Columns.Add("Salary", typeof(decimal));
            dataTable.Columns.Add("StartDate", typeof(DateTime));
            dataTable.Columns.Add("EmployeeType", typeof(int));
            dataTable.Columns.Add("Code", typeof(string));
            dataTable.Columns.Add("UserId", typeof(int));
            dataTable.Columns.Add("BranchId", typeof(int));

            int userId = _identityService.GetUserId();
            var departments = await _departmentService.GetDepartmentCodes(userId);
            var branchs = await _branchService.GetBranchesCodes(userId);
            var employeeRunningNumber = await _employeeService.GetRunningNumberByDepartmentCode(userId);
            foreach (var department in departments)
            {
                if (employeeRunningNumber.ContainsKey(department.Key) == false)
                    employeeRunningNumber.Add(department.Key, 0);
            }

            string fileName = Path.GetFileName(formFile.FileName);
            string filePath = Path.Combine(path, fileName);
            var departmentCodesExcel = new HashSet<string>();
            var employeeToValidate = new List<EmployeeViewModel>();
            using (FileStream stream = new FileStream(filePath, FileMode.Create))
            {
                await formFile.CopyToAsync(stream);
                using var workbook = new XLWorkbook(stream);
                var worksheet = workbook.Worksheet(1);
                // Loop through rows and columns to read data
                for (int i = 2; i <= worksheet.RowsUsed().Count(); i++)
                {
                    var row = worksheet.Row(i);
                    string firstName = row.Cell(1).Value.ToString();
                    string lastName = row.Cell(2).Value.ToString();
                    string phone = row.Cell(3).Value.ToString();
                    string email = row.Cell(4).Value.ToString();
                    string depCodeExcel = row.Cell(5).Value.ToString();
                    int? departmentId = Convert.ToInt32(departments.ContainsKey(depCodeExcel)
                        ? departments[depCodeExcel]
                        : null);
                    //string departmentCode = row.Cell(5).Value.ToString();
                    string salary = row.Cell(6).Value.ToString();
                    DateTime startDate = row.Cell(7).Value.GetDateTime();
                    string employeeType = row.Cell(8).Value.ToString();
                    string branchCodeExcel = row.Cell(9).Value.ToString();
                    int? branchId = Convert.ToInt32(branchs.ContainsKey(branchCodeExcel)
                        ? branchs[branchCodeExcel]
                        : null);

                    // Create a new DataRow and add it to the DataTable
                    DataRow dataRow = dataTable.NewRow();
                    dataRow["FirstName"] = firstName;
                    dataRow["LastName"] = lastName;
                    dataRow["Email"] = email;
                    dataRow["Phone"] = phone;
                    dataRow["Active"] = true;
                    dataRow["DepartmentId"] = departmentId.HasValue ? departmentId.Value : DBNull.Value;
                    dataRow["Salary"] = salary;
                    dataRow["StartDate"] = startDate;
                    dataRow["EmployeeType"] = employeeType;
                    dataRow["BranchId"] = branchId.HasValue ? branchId.Value : DBNull.Value;

                    departmentCodesExcel.Add(depCodeExcel);
                    if (departments.ContainsKey(depCodeExcel))
                        dataRow["Code"] = $"{depCodeExcel}{++employeeRunningNumber[depCodeExcel]:D4}";

                    dataRow["UserId"] = userId;

                    dataTable.Rows.Add(dataRow);
                    employeeToValidate.Add(new EmployeeViewModel
                    {
                        FirstName = firstName,
                        LastName = lastName,
                    });
                }
            }

            #region "Validate Data"
            var invalidDepartmentCodes = departmentCodesExcel.Except(departments.Keys).ToList();
            if (invalidDepartmentCodes.Count != 0)
            {
                var message = new StringBuilder("โปรดกรอกข้อมูลแผนกให้ถูกต้อง");
                invalidDepartmentCodes.ForEach(x =>
                {
                    message.Append($"<br/>{x} - ไม่พบข้อมูลแผนก");
                });
                throw new AppException(message.ToString());
            }


            var duplicatedEmployee = _employeeService.GetDuplicatedEmployee(employeeToValidate);
            if (duplicatedEmployee != null && duplicatedEmployee.Count > 0)
            {
                var message = new StringBuilder($"พบพนักงานที่มีรายชื่อซ้ำในระบบ {duplicatedEmployee.Count} รายการ กรุณาตรวจสอบข้อมูลพนักงานอีกครั้ง");
                duplicatedEmployee.ForEach(x =>
                {
                    message.Append($"<br/>- {x.FirstName} {x.LastName}");
                });
                throw new AppException(message.ToString());
            }
            #endregion

            var connString = _configuration.GetConnectionString("DefaultConnection");
            using (var con = new SqlConnection(connString))
            {
                using SqlBulkCopy sqlBulkCopy = new(con);
                sqlBulkCopy.DestinationTableName = "dbo.Employees";

                con.Open();
                sqlBulkCopy.ColumnMappings.Add("FirstName", "FirstName");
                sqlBulkCopy.ColumnMappings.Add("LastName", "LastName");
                sqlBulkCopy.ColumnMappings.Add("Email", "Email");
                sqlBulkCopy.ColumnMappings.Add("Phone", "Phone");
                sqlBulkCopy.ColumnMappings.Add("Active", "Active");
                sqlBulkCopy.ColumnMappings.Add("DepartmentId", "DepartmentId");
                sqlBulkCopy.ColumnMappings.Add("Salary", "Salary");
                sqlBulkCopy.ColumnMappings.Add("StartDate", "StartDate");
                sqlBulkCopy.ColumnMappings.Add("EmployeeType", "EmployeeType");
                sqlBulkCopy.ColumnMappings.Add("Code", "Code");
                sqlBulkCopy.ColumnMappings.Add("UserId", "UserId");
                sqlBulkCopy.ColumnMappings.Add("BranchId", "BranchId");
                sqlBulkCopy.WriteToServer(dataTable);
                con.Close();
            }

            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("Active/{id}")]
        public async Task<IActionResult> UpdateActiveEmployee([FromRoute] int id, [FromBody] UpdateEmployeeActiveViewModel updateEmployeeViewModel)
        {
            await _employeeService.UpdateActiveEmployee(id, updateEmployeeViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }
    }

    public class EmployeeExcelColumn
    {
        public string Name { get; set; }
        public String Type { get; set; }
        public string ExampleValue { get; set; }
        public int Width { get; set; }
    }
}
