﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using payroll_api.Infrastructure.Authorization;
using payroll_api.Infrastructure.Helper;
using payroll_api.Infrastructure.Service;
using payroll_api.utility.Filter;
using payroll_api.utility.Helper;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UsersController(
        IUserService userService,
        IAuthenticationService authenticationService,
        IOptions<AppSettings> appSettings
    ) : ControllerBase
    {
        private readonly IUserService _userService = userService ?? throw new ArgumentNullException(nameof(userService));
        private readonly IAuthenticationService _authenticationService = authenticationService ?? throw new ArgumentNullException(nameof(authenticationService));
        private readonly AppSettings _appSettings = appSettings?.Value ?? throw new ArgumentNullException(nameof(appSettings));

        [AllowAnonymous]
        [HttpPost("authenticate")]
        [ProducesResponseType(typeof(AuthenticateResponseViewModel), 200)]
        public IActionResult Authenticate(AuthenticateViewModel model)
        {
            var result = _authenticationService.Authenticate(model, IpAddress());
            SetTokenCookie(result.RefreshToken);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [AllowAnonymous]
        [HttpPost("refresh-token")]
        [ProducesResponseType(typeof(AuthenticateResponseViewModel), 200)]
        public IActionResult RefreshToken()
        {
            var refreshToken = Request.Cookies["refreshToken"];
            var result = _authenticationService.RefreshToken(refreshToken, IpAddress());
            SetTokenCookie(result.RefreshToken);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPost("revoke-token")]
        [ProducesResponseType(typeof(RevokeTokenViewModel), 200)]
        public IActionResult RevokeToken()
        {
            var token = Request.Cookies["refreshToken"];
            if (string.IsNullOrEmpty(token))
                return BadRequest(new { message = "Token is required" });

            _authenticationService.RevokeToken(token, IpAddress());
            return Ok(new { message = "Token revoked" });
        }

        [HttpGet("{ID}")]
        [ProducesResponseType(typeof(UserViewModel), 200)]
        public async Task<IActionResult> GetUser([FromRoute] int ID)
        {
            var result = await _userService.GetById(ID);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [AllowAnonymous]
        [HttpPost("Register", Name = "Register")]
        public async Task<IActionResult> Register([FromBody] RegisterUserViewModel registerUserViewModel)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            await _userService.Register(registerUserViewModel);

            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [AllowAnonymous]
        [HttpPost("Verify-Email")]
        public async Task<IActionResult> VerifyEmail([FromBody] EmailVerificationViewModel emailVerificationViewModel)
        {
            await _userService.VerifyEmail(emailVerificationViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [AllowAnonymous]
        [HttpPost("Resend-Email-Verification")]
        public async Task<IActionResult> ResendEmailVerification([FromBody] ResendEmailVerificationViewModel resendEmailVerificationViewModel)
        {
            await _userService.ResendEmailVerification(resendEmailVerificationViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpGet("page")]
        public async Task<IActionResult> GetUsersAsync([FromQuery] PageFilterViewModel pageFilterViewModel)
        {
            var filter = new PageFilterViewModel(
                            pageFilterViewModel.PageIndex,
                            pageFilterViewModel.PageSize,
                            pageFilterViewModel.SearchTerm,
                            pageFilterViewModel.SortBy);
            var result = await _userService.GetUsers(filter);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpDelete("{encryptId}")]
        public async Task<IActionResult> DeleteUser([FromRoute] string encryptId)
        {
            var id = Convert.ToInt32(AesEncryption.Decryption(encryptId));
            await _userService.DeleteUser(id);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("{encryptId}", Name = "UpdateUser")]
        public async Task<IActionResult> UpdateUser([FromRoute] string encryptId, [FromBody] UpdateUserViewModel updateUserViewModel)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            var id = Convert.ToInt32(AesEncryption.Decryption(encryptId));
            await _userService.UpdateUser(id, updateUserViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("Profile", Name = "UpdateUserProfile")]
        public async Task<IActionResult> UpdateUserProfile([FromBody] UserProfileViewModel userProfileViewModel)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
            await _userService.UpdateUserProfile(userProfileViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        private void SetTokenCookie(string token)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                SameSite = SameSiteMode.None,
                Secure = true,
                Expires = DateTimeOffset.UtcNow.AddDays(_appSettings.RefreshTokenExpire)
            };
            Response.Cookies.Append("refreshToken", token, cookieOptions);
        }

        private string IpAddress()
        {
            // get source ip address for the current request
            if (Request.Headers.ContainsKey("X-Forwarded-For"))
                return Request.Headers["X-Forwarded-For"];
            else
                return HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
        }
    }
}
