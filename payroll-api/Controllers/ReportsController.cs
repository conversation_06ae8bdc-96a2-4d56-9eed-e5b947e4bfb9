using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.service;
using payroll_api.utility.Helper;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ReportsController(IReportService reportService) : ControllerBase
    {
        private readonly IReportService _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));

        [HttpGet("EmployeeSlipReport")]
        public async Task<IActionResult> GetEmployeeSlipReport([FromQuery] string employeeId, [FromQuery] string payrollId)
        {
            employeeId = AesEncryption.Decryption(employeeId);
            payrollId = AesEncryption.Decryption(payrollId);

            var result = await _reportService.GetEmployeeSlipReport(employeeId, payrollId);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }
    }
}
