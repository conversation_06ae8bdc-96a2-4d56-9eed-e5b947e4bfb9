﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.Infrastructure.Service;
using payroll_api.service;
using payroll_api.utility.ViewModel;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SettingsController(
        ISettingService settingService,
        IIdentityService identityService
        ) : ControllerBase
    {
        private readonly ISettingService _settingService = settingService ?? throw new ArgumentNullException(nameof(settingService));
        private readonly IIdentityService _identityService = identityService ?? throw new ArgumentNullException(nameof(identityService));

        [HttpGet("Readonly", Name = "Get Readonly Settings")]
        public async Task<IActionResult> GetReadonlySettings()
        {
            var userId = _identityService.GetUserId();
            var result = await _settingService.GetReadonlySettings(userId);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet(Name = "Get Settings")]
        public async Task<IActionResult> GetSettings()
        {
            var userId = _identityService.GetUserId();
            var result = await _settingService.GetSettings(userId);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPost(Name = "Save Settings")]
        public async Task<IActionResult> Post([FromBody] UpdateSettingViewModel updateSettingViewModel)
        {
            await _settingService.SaveSettings(updateSettingViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }
    }
}

