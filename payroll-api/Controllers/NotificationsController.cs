using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.service;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationsController(INotificationService notificationService) : ControllerBase
    {
        private readonly INotificationService _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var result = await _notificationService.GetNotifications();
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }
    }
}
