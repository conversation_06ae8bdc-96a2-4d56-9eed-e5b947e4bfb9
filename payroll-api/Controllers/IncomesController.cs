﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using payroll_api.Infrastructure.Authorization;
using payroll_api.service;
using payroll_api.utility.Filter;
using payroll_api.utility.ViewModel;

namespace payroll_api.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class IncomesController(
        IIncomeService incomeService
        ) : ControllerBase
    {
        private readonly IIncomeService _incomeService = incomeService;

        [HttpGet]
        public async Task<IActionResult> GetIncomes([FromQuery] bool? active)
        {
            var result = await _incomeService.GetIncomes(active);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpGet("page")]
        public async Task<IActionResult> GetIncomePage([FromQuery] PageFilterViewModel pageFilterViewModel)
        {
            var validFilter = new PageFilterViewModel(pageFilterViewModel.PageIndex, pageFilterViewModel.PageSize);
            var result = await _incomeService.GetIncomePage(validFilter);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK, result));
        }

        [HttpPost]
        public async Task<IActionResult> CreateIncomes([FromBody] IncomeViewModel incomeViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            await _incomeService.CreateIncome(incomeViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateIncomes([FromRoute] int id, [FromBody] UpdateIncomeViewModel incomeViewModel)
        {
            await _incomeService.UpdateIncome(id, incomeViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }

        [HttpPut("Active/{id}")]
        public async Task<IActionResult> UpdateActiveBranch([FromRoute] int id, [FromBody] UpdateIncomeActiveViewModel updateIncomeActiveViewModel)
        {
            await _incomeService.UpdateActiveIncome(id, updateIncomeActiveViewModel);
            return Ok(new BaseResponseViewModel(true, HttpStatusCode.OK));
        }
    }
}
