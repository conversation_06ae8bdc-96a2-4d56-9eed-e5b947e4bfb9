using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.FileProviders;
using payroll_api.data;
using payroll_api.Infrastructure.Authorization;
using payroll_api.Infrastructure.Exception;
using payroll_api.Infrastructure.Extension;
using payroll_api.Infrastructure.Helper;
using QuestPDF.Drawing;
using QuestPDF.Infrastructure;
using System.IO.Compression;
using Serilog;
using FluentValidation.AspNetCore;
using FluentValidation;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
// Allow CORS
//builder.Services.AddCors();
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(builder =>
        builder.AllowAnyOrigin()
        .AllowAnyMethod()
        .AllowAnyHeader()
    );
});

builder.Services.AddQuartz();
builder.Services.Configure<AppSettings>(builder.Configuration.GetSection("AppSettings"));
builder.Services.AddDbContext(builder.Configuration);
builder.Services.AddSingleton<DapperContext>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddCustomServices();
builder.Services.AddAutoMapper();
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
});
builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});
builder.Services.Configure<GzipCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Optimal;
});
builder.Services.AddCustomEmail(builder.Configuration);

builder.Services
    .AddFluentValidationAutoValidation()
    .AddValidatorsFromAssemblyContaining<Program>();

// Configure Serilog
builder.Host.UseSerilog((hostingContext, loggerConfiguration) =>
{
    var logPath = builder.Configuration["Logging:PathFormat:LogPath"] ?? "";
    var seqSettings = builder.Configuration.GetSection("Logging:Seq");
    var seqUri = seqSettings.GetValue<string>("Uri") ?? "";
    var seqApiKey = seqSettings.GetValue<string>("ApiKey") ?? "";
    loggerConfiguration
        .MinimumLevel.Information()
        .Enrich.FromLogContext()
        .WriteTo.Console()
        .WriteTo.File(logPath, rollingInterval: RollingInterval.Day)
        .WriteTo.Seq(seqUri, apiKey: seqApiKey)
        .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Database.Command", Serilog.Events.LogEventLevel.Warning);

    var emailLogPath = builder.Configuration["Logging:PathFormat:EmailPath"] ?? "";
    loggerConfiguration
        .WriteTo.Logger(emailLogger =>
        {
            emailLogger.Filter.ByIncludingOnly(logEvent => logEvent.Properties.ContainsKey("Email"))
            .WriteTo.File(emailLogPath, rollingInterval: RollingInterval.Day);
        });
});

// Add configuration for environment variables
builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true)
    .AddEnvironmentVariables();

if (builder.Environment.IsDevelopment())
{
    builder.Configuration.AddUserSecrets<Program>();
}

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

//app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.UseCors(x =>
    x.SetIsOriginAllowed(origin => true)
    .AllowAnyHeader()
    .AllowAnyMethod()
    .AllowCredentials()
);

// global error handler
app.UseMiddleware<ErrorHandlerMiddleware>();

// custom jwt auth middleware
app.UseMiddleware<JwtMiddleware>();

QuestPDF.Settings.License = LicenseType.Community;
var contentRootPath = app.Environment.ContentRootPath;
var fontPath = Path.Combine(contentRootPath, "Fonts", "Sarabun-Light.ttf");
var fontBoldPath = Path.Combine(contentRootPath, "Fonts", "Sarabun-Bold.ttf");
using (var fs = new FileStream(fontPath, FileMode.Open))
{
    FontManager.RegisterFontWithCustomName("Sarabun-Regular", fs);
    using var fsBold = new FileStream(fontBoldPath, FileMode.Open);
    FontManager.RegisterFontWithCustomName("Sarabun-Bold", fsBold);
}

app.UseResponseCompression();

app.MigrateDatabase();

// app.UseStaticFiles();

app.UseStaticFiles(
    new StaticFileOptions()
    {
        FileProvider = new PhysicalFileProvider(
            Path.Combine(Directory.GetCurrentDirectory(), @"Images")),
        RequestPath = new PathString("/images")
    }
);

app.Run();
