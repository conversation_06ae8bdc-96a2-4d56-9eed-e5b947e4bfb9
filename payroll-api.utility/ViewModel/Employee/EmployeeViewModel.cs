﻿using payroll_api.utility.Enum;

namespace payroll_api.utility.ViewModel
{
    public class EmployeeViewModel
    {
        public int Id { get; set; }
        public string FullName { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public bool Active { get; set; }
        public int DepartmentId { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public string Branch { get; set; }
        public decimal Salary { get; set; }
        public int EmployeeType { get; set; }
        public string EmployeeTypeName { get; set; }
        public string Code { get; set; }
        public bool IsSocialSecurity { get; set; }
        public decimal SocialSecurityFund { get; set; }
        public decimal Tax { get; set; }
        public int? BranchId { get; set; }
        public string BranchName { get; set; }
    }
}
