namespace payroll_api.utility.ViewModel
{
    public class UpdateEmployeeViewModel
    {
        public int Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public int DepartmentId { get; set; }
        public int? PositionId { get; set; }
        public int? BranchId { get; set; }
        public int EmployeeType { get; set; }
        public decimal Salary { get; set; }
        public DateTime StartDate { get; set; }
        public bool IsSocialSecurity { get; set; }
        public string? Bank { get; set; }
        public string? BankAccount { get; set; }
        public AddressViewModel? Address { get; set; }
        public bool Active { get; set; }
    }
    public class UpdateEmployeeActiveViewModel
    {
        public bool Active { get; set; }
    }
}
