﻿namespace payroll_api.utility.Filter
{
    public class PageFilterViewModel
    {
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }

        public PageFilterViewModel()
        {
            PageIndex = 0;
            PageSize = 10;
        }

        public PageFilterViewModel(int pageNumber, int pageSize)
        {
            PageIndex = pageNumber < 1 ? 0 : pageNumber;
            PageSize = pageSize > 10 ? pageSize : 10;
        }

        public PageFilterViewModel(int pageNumber, int pageSize, string? sortBy)
        {
            PageIndex = pageNumber < 1 ? 0 : pageNumber;
            PageSize = pageSize > 10 ? pageSize : 10;
            SortBy = sortBy;
        }

        public PageFilterViewModel(int pageNumber, int pageSize, string? searchTerm, string? sortBy)
        {
            PageIndex = pageNumber < 1 ? 0 : pageNumber;
            PageSize = pageSize > 10 ? pageSize : 10;
            SearchTerm = searchTerm;
            SortBy = sortBy;
        }

        public string GetSortQuery(string defaultSortQuery)
        {
            if (string.IsNullOrWhiteSpace(SortBy))
                return $" {defaultSortQuery}";

            var splitSortBy = SortBy.Split(',');
            var sqlOrder = " ORDER BY";
            for (int i = 0; i < splitSortBy.Length; i++)
            {
                var splitSortByDirection = splitSortBy[i].Split(':');
                sqlOrder += i > 0 ? ", " : " ";
                sqlOrder = sqlOrder + splitSortByDirection[0] + " " + splitSortByDirection[1];
            }

            return sqlOrder;
        }
    }
}
