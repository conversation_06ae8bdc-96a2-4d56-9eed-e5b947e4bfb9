namespace payroll_api.utility.Filter
{
    public class EmployeeSalaryHistoryFilterViewModel : PageFilterViewModel
    {
        public EmployeeSalaryHistoryFilterViewModel()
        {
        }

        public EmployeeSalaryHistoryFilterViewModel(
            int pageIndex,
            int pageSize,
            int employeeId,
            string? sortBy)
        : base(pageIndex, pageSize)
        {
            EmployeeId = employeeId;
            SortBy = sortBy;
        }

        public int EmployeeId { get; set; }
    }
}
