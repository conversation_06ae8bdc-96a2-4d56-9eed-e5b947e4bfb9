﻿using payroll_api.utility.Enum;
using System.Text.Json.Serialization;

namespace payroll_api.utility.ViewModel
{
    public class AuthenticateResponseViewModel
    {
        public AuthenticateResponseViewModel(
            int id,
            string firstName,
            string lastName,
            string email,
            UserType userType,
            int packageType,
            DateTime expireDate,
            string token,
            string refreshToken)
        {
            ID = id;
            FirstName = firstName;
            LastName = lastName;
            Email = email;
            UserType = userType;
            PackageType = packageType;
            ExpireDate = expireDate;
            JwtToken = token;
            RefreshToken = refreshToken;
        }

        public int ID { get; set; }
        public string FirstName { get; private set; }
        public string LastName { get; private set; }
        public string Email { get; set; }
        public string JwtToken { get; set; }
        public UserType UserType { get; set; }
        [JsonIgnore]
        public string RefreshToken { get; set; }
        public int PackageType { get; set; }
        public DateTime ExpireDate { get; set; }
    }
}
