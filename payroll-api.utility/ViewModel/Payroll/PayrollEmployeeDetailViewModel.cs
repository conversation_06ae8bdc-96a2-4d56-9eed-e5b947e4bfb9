﻿namespace payroll_api.utility.ViewModel
{
    public class PayrollEmployeeDetailViewModel {
        public int PayrollId { get; set; }
        public int EmployeeId { get; set; }
        public decimal Salary { get; set; }
        public decimal Tax { get; set; }
        public decimal SocialSecurityFund { get; set; }
        public decimal TotalIncome { get; set; }
        public decimal TotalOutcome { get; set; }
        public decimal Total { get; set; }
        public string EmployeeCode { get; set; }
        public string EmployeeEmail { get; set; }
        public string EmployeeFirstName { get; set; }
        public string EmployeeLastName { get; set; }
        public List<PayrollEmployeeIncomeViewModel> PayrollEmployeeIncomes { get; set; }
        public List<PayrollEmployeeOutcomeViewModel> PayrollEmployeeOutcomes { get; set; }
    }
}
