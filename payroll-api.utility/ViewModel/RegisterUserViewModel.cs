﻿using System.ComponentModel.DataAnnotations;

namespace payroll_api.utility.ViewModel
{
    public class RegisterUserViewModel
    {
        [Required(ErrorMessage = "โปรดกรอกชื่อ")]
        [StringLength(50)]
        public string FirstName { get; set; }

        [Required(ErrorMessage = "โปรดกรอกนามสกุล")]
        [StringLength(50)]
        public string LastName { get; set; }

        [Required]
        [StringLength(50)]
        public string Username { get; set; }

        [Required(ErrorMessage = "โปรดกรอกอีเมล")]
        [StringLength(100)]
        [EmailAddress(ErrorMessage = "อีเมลไม่ถูกต้อง")]
        public string Email { get; set; }

        [Required]
        public string Password { get; set; }

        [Required]
        [Compare("Password")]
        public string ConfirmPassword { get; set; }

        [Required]
        public string VerifyEmailURL { get; set; }
    }
}
