using System.ComponentModel.DataAnnotations;

namespace payroll_api.utility.ViewModel
{
    public class UserProfileViewModel
    {
        [StringLength(50)]
        [Required(ErrorMessage = "โปรดกรอกชื่อ")]
        public string FirstName { get; set; } = "";

        [StringLength(50)]
        [Required(ErrorMessage = "โปรดกรอกนามสกุล")]
        public string LastName { get; set; } = "";

        [StringLength(100)]
        [Required(ErrorMessage = "โปรดกรอกอีเมล")]
        [EmailAddress]
        public string Email { get; set; } = "";
    }
}
