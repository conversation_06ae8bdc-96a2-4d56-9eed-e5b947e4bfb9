﻿using System.ComponentModel.DataAnnotations;

namespace payroll_api.utility.ViewModel
{
    public class UserDetailViewModel
    {
        public int ID { get; set; }

        [StringLength(50)]
        [Required(ErrorMessage = "โปรดกรอกชื่อ")]
        public string FirstName { get; set; }

        [StringLength(50)]
        [Required(ErrorMessage = "โปรดกรอกนามสกุล")]
        public string LastName { get; set; }

        [StringLength(100)]
        [Required(ErrorMessage = "โปรดกรอกอีเมล")]
        [EmailAddress(ErrorMessage = "อีเมลไม่ถูกต้อง")]
        public string Email { get; set; }
        public int PackageType { get; set; }
        public DateTime ExpireDate { get; set; }
        public string FullName { get; set; }
        public string PackageTypeName { get; set; }
    }
}
