namespace payroll_api.utility.ViewModel
{
    public class BranchViewModel {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public bool Active { get; set; }
        public int TotalEmployee { get; set; }
    }
    public class BranchDetailViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public bool Active { get; set; }
        public AddressViewModel? Address { get; set; }
    }
    public class UpdateBranchViewModel : BranchDetailViewModel
    {
    }
    public class UpdateBranchActiveViewModel
    {
        public bool Active { get; set; }
    }
}
