﻿using System.ComponentModel.DataAnnotations;

namespace payroll_api.utility.ViewModel
{
    public abstract class BaseDepartmentViewModel
    {
        public int Id { get; set; }

        [RegularExpression(@"^[^0-9]*$", ErrorMessage = "รหัสแผนกต้องไม่มีตัวเลข")]
        public string Code { get; set; }
        public string Name { get; set; }
        public bool Active { get; set; }
    }
    public class DepartmentViewModel : BaseDepartmentViewModel
    {
        //public string Department { get; set; }
        public int TotalEmployee { get; set; }
        public int? TutorialStepId { get; set; }
    }
}
