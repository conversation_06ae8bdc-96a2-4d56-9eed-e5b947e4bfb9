﻿using payroll_api.utility.Enum;

namespace payroll_api.utility.ViewModel
{
    public class CreatePayrollViewModel
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime DueDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<PayrollEmployeeViewModel> PayrollEmployees { get; set; }
        public decimal TotalSalary { get; set; }
        public decimal TotalIncome { get; set; }
        public decimal TotalOutcome { get; set; }
        public decimal TotalSocialSecurityFund { get; set; }
        public decimal TotalTax { get; set; }
        public decimal Total { get; set; }
        public PayrollStatus Status { get; set; }
        public decimal SocialSecurityFundEmployer { get; set; }
        public int? TutorialStepId { get; set; }
    }
}
