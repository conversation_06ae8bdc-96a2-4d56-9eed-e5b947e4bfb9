﻿namespace payroll_api.utility.ViewModel.Reports
{
    public class EmployeeSlipReportViewModel
    {
        public string Code { get; set; }
        public DateTime? PayDate { get; set; }
        //public DateTime StartDate { get; set; }
        //public DateTime EndDate { get; set; }
        public DateTime PayrollDate { get; set; }
        public decimal Salary { get; set; }
        public decimal SocialSecurityFund { get; set; }
        public decimal Tax { get; set; }
        public decimal TotalIncome { get; set; }
        public decimal TotalOutcome { get; set; }
        public decimal Total { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string EmployeeCode { get; set; }
        public string PositionName { get; set; }
        public string PayMethod { get; set; }
        public string Bank { get; set; }
        public string BankAccount { get; set; }
        public EmployeeSlipReportSummaryViewModel Summary { get; set; }
        public IList<PayrollEmployeeIncomeViewModel> PayrollEmployeeIncomes { get; set; }
        public IList<PayrollEmployeeOutcomeViewModel> PayrollEmployeeOutcomes { get; set; }
        public int PayrollEmployeeId { get; set; }
        public string EmployeeEmail { get; set; }
    }
    public class EmployeeSlipReportSummaryViewModel
    {
        public decimal TotalIncome { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalSocialSecurityFund { get; set; }
    }
}
