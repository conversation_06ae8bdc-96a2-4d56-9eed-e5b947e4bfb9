namespace payroll_api.utility.ViewModel.Dashboards
{
    public class AllSummaryViewModel
    {
        public IEnumerable<SalaryByBranchViewModel> SalaryByBranch { get; set; }
        public IEnumerable<SalaryByPositionViewModel> SalaryByPosition { get; set; }
        public IEnumerable<SalaryByDepartmentViewModel> SalaryByDepartment { get; set; }
        public IEnumerable<SalaryByYearViewModel> SalaryByYear { get; set; }
        public SalarySummaryViewModel SalarySummary { get; set; }
        public IEnumerable<Top10EmployeeSalaryViewModel> Top10EmployeeSalary { get; set; }
    }
}
