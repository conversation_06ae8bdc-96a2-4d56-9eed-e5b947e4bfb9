namespace payroll_api.utility.Helper
{
    public static class TaxCalculator
    {
        private static readonly decimal[] taxRates = { 0m, 0.05m, 0.1m, 0.15m, 0.2m, 0.25m, 0.3m, 0.35m };
        private static readonly decimal[] taxThresholds = { 150000m, 150000m, 200000m, 250000m, 250000m, 10000000m, 30000000m };

        public static decimal CalculateTax(decimal annualIncome)
        {
            decimal totalTax = 0m;
            decimal taxableIncome = annualIncome;

            for (int i = 0; i < taxRates.Length; i++)
            {
                if (taxableIncome <= 0)
                    break;

                decimal rate = taxRates[i];
                decimal threshold = taxThresholds[i];

                if (taxableIncome <= threshold)
                {
                    totalTax += taxableIncome * rate;
                    taxableIncome = 0;
                }
                else
                {
                    totalTax += threshold * rate;
                    taxableIncome -= threshold;
                }
            }

            return totalTax > 0 ? Math.Round(totalTax / 12, 2) : 0;
        }
    }
}