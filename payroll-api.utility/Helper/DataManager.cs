﻿
using System.Text.RegularExpressions;

namespace payroll_api.utility.Helper
{
    public static class DataManager
    {
        public static int ExtractNumericPart(string input)
        {
            Match match = Regex.Match(input, @"\d+");

            if (match.Success)
                return int.Parse(match.Value);
            else
                throw new InvalidOperationException("No numeric part found in the input.");
        }
        public static (string code, int? runningNumber) ExtractCode(string input)
        {
            string pattern = @"^(\D+)(\d+)$";

            Match match = Regex.Match(input, pattern);

            if (match.Success) {
                string prefix = match.Groups[1].Value;
                string runningNumber = match.Groups[2].Value;

                return (prefix, int.Parse(runningNumber));
            }
            else return (null, null);
        }
    }
}
