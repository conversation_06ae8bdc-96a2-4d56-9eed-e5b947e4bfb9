using System.Security.Cryptography;
using System.Text;

namespace payroll_api.utility.Helper
{
    public static class AesEncryption
    {
        public const string AES_KEY = "12345678900000001234567890000000";
        public const string AES_IV = "1234567890000000";
        public static string Decryption(string input)
        {
            byte[] inputBytes = HexStringToByteArray(input);
            byte[] keyBytes = Encoding.UTF8.GetBytes(AES_KEY[..32]);
            using var aesAlg = Aes.Create();
            aesAlg.Key = keyBytes;
            aesAlg.IV = Encoding.UTF8.GetBytes(AES_IV[..16]);

            ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);
            using var msEncrypt = new MemoryStream(inputBytes);
            using var csEncrypt = new CryptoStream(msEncrypt, decryptor, CryptoStreamMode.Read);
            using var srEncrypt = new StreamReader(csEncrypt);
            return srEncrypt.ReadToEnd();
        }

        private static byte[] HexStringToByteArray(string s)
        {
            s = s.Replace(" ", "");
            byte[] buffer = new byte[s.Length / 2];
            for (int i = 0; i < s.Length; i += 2)
                buffer[i / 2] = Convert.ToByte(s.Substring(i, 2), 16);
            return buffer;
        }
    }
}
